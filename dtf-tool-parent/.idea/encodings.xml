<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="Encoding">
    <file url="file://$PROJECT_DIR$/../dtf-tool-business/dtf-tool-business-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/../dtf-tool-business/dtf-tool-business-service/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/../dtf-tool-business/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/../dtf-tool-business/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/../dtf-tool-common/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/../dtf-tool-gateway/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/../dtf-tool-tenant/dtf-tool-tenant-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/../dtf-tool-tenant/dtf-tool-tenant-service/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/../dtf-tool-tenant/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/../dtf-tool-tenant/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/../hoson-pod-business/hoson-pod-business-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/../hoson-pod-business/hoson-pod-business-service/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/../hoson-pod-business/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/../hoson-pod-business/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/../hoson-pod-common/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/../hoson-pod-cuzcuz-ai/hoson-pod-cuzcuz-ai-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/../hoson-pod-cuzcuz-ai/hoson-pod-cuzcuz-ai-service/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/../hoson-pod-cuzcuz-ai/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/../hoson-pod-cuzcuz-ai/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/../hoson-pod-finance/hoson-pod-finance-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/../hoson-pod-finance/hoson-pod-finance-service/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/../hoson-pod-finance/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/../hoson-pod-finance/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/../hoson-pod-gateway/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/../hoson-pod-openapi/hoson-pod-openapi-sdk/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/../hoson-pod-openapi/hoson-pod-openapi-service/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/../hoson-pod-openapi/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/../hoson-pod-openapi/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/../hoson-pod-order/hoson-pod-order-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/../hoson-pod-order/hoson-pod-order-service/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/../hoson-pod-order/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/../hoson-pod-order/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/../hoson-pod-parent/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/../hoson-pod-parent/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/../hoson-pod-publish-goods/hoson-pod-publish-goods-service/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/../hoson-pod-publish-goods/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/../hoson-pod-publish-goods/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/../hoson-pod-tenant/hoson-pod-tenant-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/../hoson-pod-tenant/hoson-pod-tenant-service/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/../hoson-pod-tenant/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/../hoson-pod-tenant/src/main/resources" charset="UTF-8" />
    <file url="file:///src/main/java" charset="UTF-8" />
    <file url="file:///src/main/resources" charset="UTF-8" />
  </component>
</project>