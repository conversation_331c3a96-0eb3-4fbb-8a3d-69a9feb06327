<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="hoson-pod-publish-goods-service" />
        <module name="hoson-pod-cuzcuz-ai-service" />
        <module name="hoson-pod-openapi-sdk" />
        <module name="hoson-pod-openapi-service" />
        <module name="hoson-pod-order-service" />
        <module name="hoson-pod-business-service" />
        <module name="hoson-pod-cuzcuz-ai-api" />
        <module name="hoson-pod-order-api" />
        <module name="dtf-tool-tenant-api" />
        <module name="hoson-pod-common" />
        <module name="dtf-tool-business-api" />
        <module name="hoson-pod-gateway" />
        <module name="hoson-pod-business-api" />
        <module name="hoson-pod-tenant-api" />
        <module name="hoson-pod-finance-service" />
        <module name="hoson-pod-finance-api" />
        <module name="dtf-tool-common" />
        <module name="dtf-tool-business-service" />
        <module name="dtf-tool-tenant-service" />
        <module name="dtf-tool-gateway" />
        <module name="hoson-pod-tenant-service" />
      </profile>
    </annotationProcessing>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="dtf-tool-business-api" options="-parameters" />
      <module name="dtf-tool-business-service" options="-parameters" />
      <module name="dtf-tool-common" options="-parameters" />
      <module name="dtf-tool-gateway" options="-parameters" />
      <module name="dtf-tool-tenant-api" options="-parameters" />
      <module name="dtf-tool-tenant-service" options="-parameters" />
      <module name="hoson-pod-business-api" options="-parameters" />
      <module name="hoson-pod-business-service" options="-parameters" />
      <module name="hoson-pod-common" options="-parameters" />
      <module name="hoson-pod-cuzcuz-ai-api" options="-parameters" />
      <module name="hoson-pod-cuzcuz-ai-service" options="-parameters" />
      <module name="hoson-pod-finance" options="-parameters" />
      <module name="hoson-pod-finance-api" options="-parameters" />
      <module name="hoson-pod-finance-service" options="-parameters" />
      <module name="hoson-pod-gateway" options="-parameters" />
      <module name="hoson-pod-openapi-sdk" options="-parameters" />
      <module name="hoson-pod-openapi-service" options="-parameters" />
      <module name="hoson-pod-order-api" options="-parameters" />
      <module name="hoson-pod-order-service" options="-parameters" />
      <module name="hoson-pod-publish-goods-service" options="-parameters" />
      <module name="hoson-pod-tenant-api" options="-parameters" />
      <module name="hoson-pod-tenant-service" options="-parameters" />
    </option>
  </component>
</project>