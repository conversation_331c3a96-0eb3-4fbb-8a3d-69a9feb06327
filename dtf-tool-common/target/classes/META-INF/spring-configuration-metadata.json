{"groups": [{"name": "dtf.oss-tencent", "type": "com.dtf.tool.common.config.OssTencentStorageConfig", "sourceType": "com.dtf.tool.common.config.OssTencentStorageConfig"}, {"name": "hoson.pod.tenant", "type": "com.dtf.tool.common.config.TenantConfig", "sourceType": "com.dtf.tool.common.config.TenantConfig"}], "properties": [{"name": "dtf.oss-tencent.app-id", "type": "java.lang.Integer", "description": "腾讯云AppId", "sourceType": "com.dtf.tool.common.config.OssTencentStorageConfig"}, {"name": "dtf.oss-tencent.bucket-name", "type": "java.lang.String", "description": "腾讯云BucketName", "sourceType": "com.dtf.tool.common.config.OssTencentStorageConfig"}, {"name": "dtf.oss-tencent.domain", "type": "java.lang.String", "description": "腾讯云绑定的域名", "sourceType": "com.dtf.tool.common.config.OssTencentStorageConfig"}, {"name": "dtf.oss-tencent.prefix", "type": "java.lang.String", "description": "腾讯云路径前缀", "sourceType": "com.dtf.tool.common.config.OssTencentStorageConfig"}, {"name": "dtf.oss-tencent.region", "type": "java.lang.String", "description": "腾讯云COS所属地区", "sourceType": "com.dtf.tool.common.config.OssTencentStorageConfig"}, {"name": "dtf.oss-tencent.secret-id", "type": "java.lang.String", "description": "腾讯云SecretId", "sourceType": "com.dtf.tool.common.config.OssTencentStorageConfig"}, {"name": "dtf.oss-tencent.secret-key", "type": "java.lang.String", "description": "腾讯云SecretKey", "sourceType": "com.dtf.tool.common.config.OssTencentStorageConfig"}, {"name": "hoson.pod.tenant.enabled", "type": "java.lang.Bo<PERSON>an", "description": "多租户开关", "sourceType": "com.dtf.tool.common.config.TenantConfig"}, {"name": "hoson.pod.tenant.ignore-tables", "type": "java.util.List<java.lang.String>", "description": "忽略租户的表名列表 这些表不需要添加租户条件", "sourceType": "com.dtf.tool.common.config.TenantConfig"}, {"name": "hoson.pod.tenant.tenant-id-column", "type": "java.lang.String", "description": "租户字段名", "sourceType": "com.dtf.tool.common.config.TenantConfig"}], "hints": []}