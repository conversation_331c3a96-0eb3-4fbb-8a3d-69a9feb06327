-- =====================================================
-- DTF多租户用户体系核心架构表设计 v4.0 - 美国合规优化版
-- 基于美国CCPA、GDPR、SOX法规要求，优化字段设计
-- 核心优化：数值编码替代字符串枚举，敏感数据加密，性能优化
-- =====================================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =====================================================
-- 状态码映射表 - 便于维护和查询
-- =====================================================

CREATE TABLE dtf_status_code_mapping
(
    id              BIGINT         NOT NULL AUTO_INCREMENT COMMENT '映射ID',
    code_type       VARCHAR(50)    NOT NULL COMMENT '代码类型',
    code_value      TINYINT        NOT NULL COMMENT '数值编码',
    code_name       VARCHAR(50)    NOT NULL COMMENT '显示名称',
    code_description VARCHAR(200) COMMENT '描述',
    is_active       TINYINT        NOT NULL DEFAULT 1 COMMENT '是否启用',
    sort_order      INT            NOT NULL DEFAULT 0 COMMENT '排序',
    
    -- 审计字段
    create_by       VARCHAR(64) COMMENT '创建人',
    update_by       VARCHAR(64) COMMENT '更新人',
    created_at      DATETIME       NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at      DATETIME       NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    version         INT            DEFAULT 1 NOT NULL COMMENT '版本号',
    deleted         TINYINT        DEFAULT 0 NOT NULL COMMENT '删除标记',
    
    PRIMARY KEY (id),
    UNIQUE KEY uk_code_type_value (code_type, code_value),
    KEY idx_code_type (code_type),
    KEY idx_is_active (is_active)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci
COMMENT = '状态码映射表，支持数值编码和显示名称的转换';

-- 初始化状态码映射数据
INSERT INTO dtf_status_code_mapping (code_type, code_value, code_name, code_description) VALUES
-- 全局状态
('GLOBAL_STATUS', 0, 'ACTIVE', '活跃'),
('GLOBAL_STATUS', 1, 'SUSPENDED', '暂停'),
('GLOBAL_STATUS', 2, 'DELETED', '已删除'),

-- 用户类型
('USER_TYPE', 0, 'CONSUMER', '消费者'),
('USER_TYPE', 1, 'VENDOR_ADMIN', '厂商管理员'),
('USER_TYPE', 2, 'AGENT', '代理商'),
('USER_TYPE', 3, 'SUPPORT', '客服'),
('USER_TYPE', 4, 'DEVELOPER', '开发者'),

-- 租户类型
('TENANT_TYPE', 0, 'OFFICIAL', '官方'),
('TENANT_TYPE', 1, 'DEVICE_VENDOR', '设备厂商'),
('TENANT_TYPE', 2, 'AGENT', '代理商'),

-- 部署类型
('DEPLOYMENT_TYPE', 0, 'SAAS', '软件即服务'),
('DEPLOYMENT_TYPE', 1, 'PRIVATE_CLOUD', '私有云'),
('DEPLOYMENT_TYPE', 2, 'HYBRID', '混合云'),
('DEPLOYMENT_TYPE', 3, 'ON_PREMISE', '本地部署'),

-- 性别
('GENDER', 0, 'UNKNOWN', '未知'),
('GENDER', 1, 'MALE', '男性'),
('GENDER', 2, 'FEMALE', '女性'),
('GENDER', 3, 'OTHER', '其他'),

-- 验证级别
('VERIFICATION_LEVEL', 0, 'UNVERIFIED', '未验证'),
('VERIFICATION_LEVEL', 1, 'EMAIL_VERIFIED', '邮箱已验证'),
('VERIFICATION_LEVEL', 2, 'PHONE_VERIFIED', '手机已验证'),
('VERIFICATION_LEVEL', 3, 'REAL_NAME_VERIFIED', '实名已验证');

-- =====================================================
-- 第一层：全局身份管理层 - 优化版
-- 解决跨租户用户识别和隐私保护，符合CCPA要求
-- =====================================================

CREATE TABLE dtf_global_identity
(
    id                   BIGINT                                                 NOT NULL AUTO_INCREMENT COMMENT '全局身份ID',
    global_user_id       VARCHAR(64)                                            NOT NULL UNIQUE COMMENT '全局唯一用户标识(UUID)',

    -- 隐私保护设计 (CCPA必需) - 数值编码优化
    email_hash           VARCHAR(64)                                            NOT NULL COMMENT '邮箱SHA256哈希，用于关联检测',
    phone_hash           VARCHAR(64) COMMENT '手机号哈希',
    identity_fingerprint VARCHAR(128) COMMENT '身份指纹(设备+行为特征)，防欺诈必需',

    -- 身份属性 (业务必需) - 数值编码
    identity_type        TINYINT                                                NOT NULL DEFAULT 0 COMMENT '身份类型：0-消费者,1-厂商管理员,2-代理商,3-官方',
    verification_level   TINYINT                                                NOT NULL DEFAULT 0 COMMENT '验证级别：0-未验证,1-邮箱验证,2-手机验证,3-实名验证',
    risk_score           DECIMAL(3, 2)                                                   DEFAULT 0.00 COMMENT '风险评分0-1，金融业务必需',

    -- 全局状态 - 数值编码
    global_status        TINYINT                                                NOT NULL DEFAULT 0 COMMENT '全局状态：0-活跃,1-暂停,2-已删除',
    suspension_reason    VARCHAR(200) COMMENT '暂停原因',

    -- 合规字段 (CCPA/GDPR必需)
    gdpr_consent_version VARCHAR(20) COMMENT 'GDPR同意版本，跨境用户可能需要',
    data_retention_until DATE COMMENT '数据保留截止日期，CCPA自动删除必需',
    last_privacy_policy_accepted_at DATETIME COMMENT '最后接受隐私政策时间，合规审计必需',
    
    -- 数据主体权利支持 (CCPA必需)
    data_portability_requested TINYINT DEFAULT 0 COMMENT '数据可携带性请求',
    data_deletion_requested TINYINT DEFAULT 0 COMMENT '数据删除请求',
    data_access_requested TINYINT DEFAULT 0 COMMENT '数据访问请求',

    -- 审计字段
    create_by            VARCHAR(64) COMMENT '创建人',
    update_by            VARCHAR(64) COMMENT '更新人',
    created_at           DATETIME                                               NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at           DATETIME                                               NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    version              int                                                             default 1 not null comment '版本号',
    deleted              tinyint                                                         default 0 not null comment '删除标记',

    PRIMARY KEY (id),
    UNIQUE KEY uk_global_user_id (global_user_id),
    KEY idx_email_hash (email_hash),
    KEY idx_identity_type_status (identity_type, global_status),
    KEY idx_verification_level (verification_level),
    KEY idx_created_at (created_at),
    KEY idx_deleted (deleted),
    KEY idx_data_retention (data_retention_until)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
    COMMENT ='全局用户身份表，支持跨租户用户识别和隐私保护，符合CCPA要求';

-- =====================================================
-- 第二层：部署实例管理层 - 优化版
-- 支持AWS部署和美国合规要求
-- =====================================================

CREATE TABLE dtf_deployment_instance
(
    id                        BIGINT                                                     NOT NULL AUTO_INCREMENT COMMENT '部署实例ID',
    instance_id               VARCHAR(64)                                                NOT NULL UNIQUE COMMENT '实例唯一标识',
    instance_name             VARCHAR(100)                                               NOT NULL COMMENT '实例名称',
    deployment_type           TINYINT                                                    NOT NULL DEFAULT 0 COMMENT '部署类型：0-SAAS,1-私有云,2-混合云,3-本地部署',

    -- 网络配置 (AWS部署必需)
    primary_domain            VARCHAR(100)                                               NOT NULL COMMENT '主域名',
    api_gateway_url           VARCHAR(200) COMMENT 'API网关地址',
    cdn_domain                VARCHAR(100) COMMENT 'CDN域名',

    -- 基础设施配置 (运维必需，单人开发需要灵活配置)
    database_config           JSON COMMENT '数据库配置信息',
    storage_config            JSON COMMENT '存储配置信息(AWS S3等)',
    cache_config              JSON COMMENT '缓存配置信息(Redis等)',

    -- 地理位置和合规 (美国法规必需)
    region_code               VARCHAR(10)                                                NOT NULL COMMENT '部署区域：US-EAST-1/US-WEST-2等',
    data_residency_rules      JSON COMMENT '数据驻留规则，州法律要求',
    compliance_certifications JSON COMMENT '合规认证列表(SOX/PCI/HIPAA等)',

    -- 实例状态 - 数值编码
    instance_status           TINYINT                                                    NOT NULL DEFAULT 0 COMMENT '实例状态：0-运行中,1-已停止,2-维护中,3-已废弃',

    -- 审计字段
    create_by            VARCHAR(64) COMMENT '创建人',
    update_by            VARCHAR(64) COMMENT '更新人',
    created_at           DATETIME                                               NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at           DATETIME                                               NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    version              int                                                             default 1 not null comment '版本号',
    deleted              tinyint                                                         default 0 not null comment '删除标记',

    PRIMARY KEY (id),
    UNIQUE KEY uk_instance_id (instance_id),
    KEY idx_deployment_type (deployment_type),
    KEY idx_region_code (region_code),
    KEY idx_instance_status (instance_status)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
    COMMENT ='部署实例表，支持AWS部署和美国合规要求';

-- =====================================================
-- 第三层：租户管理层 - 优化版
-- 支持多层级租户和代理商体系，符合商业化需求
-- =====================================================

CREATE TABLE dtf_tenant
(
    id                       BIGINT                                                                 NOT NULL AUTO_INCREMENT COMMENT '租户ID',
    tenant_code              VARCHAR(50)                                                            NOT NULL UNIQUE COMMENT '租户代码',
    tenant_name              VARCHAR(100)                                                           NOT NULL COMMENT '租户名称',
    tenant_name_en           VARCHAR(100) COMMENT '英文名称',
    tenant_type              TINYINT                                                                NOT NULL DEFAULT 1 COMMENT '租户类型：0-官方,1-设备厂商,2-代理商',

    -- 层级关系 (代理商体系必需，删除后难以重建)
    parent_tenant_id         BIGINT COMMENT '父租户ID',
    tenant_hierarchy         VARCHAR(200) COMMENT '租户层级路径，如/1/2/3',
    hierarchy_level          INT                                                                             DEFAULT 0 COMMENT '层级深度',

    -- 部署配置
    deployment_instance_id   BIGINT                                                                 NOT NULL COMMENT '部署实例ID',
    isolation_level          TINYINT                                                                         DEFAULT 1 COMMENT '隔离级别：1-数据库,2-模式,3-表',

    -- 品牌定制 (设备厂商核心需求，竞争优势)
    brand_config             JSON COMMENT '品牌定制配置',
    theme_config             JSON COMMENT '主题配置',
    logo_url                 VARCHAR(500) COMMENT 'Logo URL',
    custom_domain            VARCHAR(100) COMMENT '自定义域名',

    -- 功能配置 (产品差异化必需)
    feature_config           JSON COMMENT '功能开关配置',
    module_permissions       JSON COMMENT '模块权限配置',
    api_rate_limits          JSON COMMENT 'API限流配置',

    -- 资源配额 (SaaS模式必需)
    user_quota               INT                                                                             DEFAULT 100 COMMENT '用户配额',
    storage_quota_gb         INT                                                                             DEFAULT 10 COMMENT '存储配额(GB)',
    api_quota_daily          INT                                                                             DEFAULT 10000 COMMENT '每日API调用配额',

    -- 计费配置 (商业化核心，后期修改成本高)
    billing_model            TINYINT                                                                         DEFAULT 0 COMMENT '计费模式：0-免费,1-订阅,2-按量付费,3-企业版',
    billing_config           JSON COMMENT '计费规则配置',
    subscription_plan        VARCHAR(50) COMMENT '订阅计划',

    -- 合规配置 (美国法规硬性要求)
    data_processing_purposes JSON COMMENT '数据处理目的，CCPA必需',
    retention_policies       JSON COMMENT '数据保留策略',
    privacy_settings         JSON COMMENT '隐私设置配置',

    -- 租户状态 - 数值编码
    tenant_status            TINYINT                                                                NOT NULL DEFAULT 0 COMMENT '租户状态：0-活跃,1-暂停,2-过期,3-已删除',

    -- 审计字段
    create_by            VARCHAR(64) COMMENT '创建人',
    update_by            VARCHAR(64) COMMENT '更新人',
    created_at           DATETIME                                               NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at           DATETIME                                               NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    version              int                                                             default 1 not null comment '版本号',
    deleted              tinyint                                                         default 0 not null comment '删除标记',

    PRIMARY KEY (id),
    UNIQUE KEY uk_tenant_code (tenant_code),
    KEY idx_tenant_type (tenant_type),
    KEY idx_deployment_instance (deployment_instance_id),
    KEY idx_parent_tenant (parent_tenant_id),
    KEY idx_tenant_status (tenant_status),
    KEY idx_hierarchy_level (hierarchy_level)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
    COMMENT ='租户表，支持多层级管理和代理商体系';

-- =====================================================
-- 第四层：租户用户账户层 - 优化版
-- 解决同邮箱多租户注册问题，符合美国安全和合规要求
-- =====================================================

CREATE TABLE dtf_tenant_user_account
(
    id                      BIGINT                                                         NOT NULL AUTO_INCREMENT COMMENT '用户账户ID',
    tenant_id               BIGINT                                                         NOT NULL COMMENT '租户ID',
    global_user_id          VARCHAR(64)                                                    NOT NULL COMMENT '全局用户标识',
    local_user_id           VARCHAR(64) COMMENT '租户内用户ID',
    username                VARCHAR(50) COMMENT '用户名',
    email                   VARCHAR(100)                                                   NOT NULL COMMENT '邮箱',

    -- 密码和安全 (美国安全要求，网络攻击频繁)
    password_hash           VARCHAR(255)                                                   NOT NULL COMMENT '密码哈希',
    password_salt           VARCHAR(32) COMMENT '密码盐值，安全加固',
    password_updated_at     DATETIME COMMENT '密码更新时间，密码策略必需',
    failed_login_attempts   TINYINT                                                                 DEFAULT 0 COMMENT '登录失败次数',
    locked_until            DATETIME COMMENT '锁定截止时间',

    -- 多因素认证 (企业客户基本安全要求)
    mfa_enabled             TINYINT                                                                 DEFAULT 0 COMMENT '是否启用MFA',
    mfa_secret              VARCHAR(32) COMMENT 'MFA密钥',
    backup_codes            JSON COMMENT '备用验证码',

    -- 用户属性 - 数值编码
    user_type               TINYINT                                                                 DEFAULT 0 COMMENT '用户类型：0-消费者,1-厂商管理员,2-代理商,3-客服,4-开发者',
    user_level              TINYINT                                                                 DEFAULT 0 COMMENT '用户级别：0-基础,1-高级,2-VIP,3-企业版',

    -- 个人信息 (实名认证、年龄验证等可能的合规要求) - 加密存储
    nickname                VARCHAR(50) COMMENT '昵称',
    real_name_encrypted     VARCHAR(255) COMMENT '真实姓名(加密)',
    avatar_url              VARCHAR(500) COMMENT '头像URL',
    gender                  TINYINT                                                                 DEFAULT 0 COMMENT '性别：0-未知,1-男性,2-女性,3-其他',
    birth_date              DATE COMMENT '出生日期，年龄验证可能需要',

    -- 联系方式 - 哈希存储
    phone_hash              VARCHAR(64) COMMENT '手机号哈希',
    phone_verified          TINYINT                                                                 DEFAULT 0 COMMENT '手机验证状态',
    address_encrypted       JSON COMMENT '地址信息(加密)，可能需要',

    -- 账户状态 - 数值编码
    account_status          TINYINT                                                                 DEFAULT 0 COMMENT '账户状态：0-待激活,1-活跃,2-暂停,3-已删除',
    suspension_reason       VARCHAR(200) COMMENT '暂停原因',
    activation_token        VARCHAR(64) COMMENT '激活令牌',
    activation_expires_at   DATETIME COMMENT '激活令牌过期时间',

    -- 业务属性 (商业化必需)
    subscription_type       VARCHAR(20)                                                             DEFAULT 'FREE' COMMENT '订阅类型',
    subscription_expires_at DATETIME COMMENT '订阅过期时间',

    -- 登录信息 (安全审计必需)
    last_login_at           DATETIME COMMENT '最后登录时间',
    last_login_ip           VARCHAR(45) COMMENT '最后登录IP，安全审计必需',
    login_count             INT                                                                     DEFAULT 0 COMMENT '登录次数',

    -- 地理和本地化 (美国市场必需，州法律适用)
    country_code            VARCHAR(10) COMMENT '国家代码',
    region_code             VARCHAR(10) COMMENT '地区代码，州法律适用',
    language                VARCHAR(10)                                                             DEFAULT 'en' COMMENT '语言偏好',
    timezone                VARCHAR(50) COMMENT '时区，时区处理必需',

    -- 合规字段 (CCPA硬性要求)
    gdpr_consent            TINYINT                                                                 DEFAULT 0 COMMENT 'GDPR同意状态',
    marketing_consent       TINYINT                                                                 DEFAULT 0 COMMENT '营销同意状态',
    data_processing_consent JSON COMMENT '数据处理同意记录，CCPA必需',

    email_verified          TINYINT                                                                 DEFAULT 0 COMMENT '邮箱验证状态',

    -- 审计字段
    create_by            VARCHAR(64) COMMENT '创建人',
    update_by            VARCHAR(64) COMMENT '更新人',
    created_at           DATETIME                                               NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at           DATETIME                                               NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    version              int                                                             default 1 not null comment '版本号',
    deleted              tinyint                                                         default 0 not null comment '删除标记',

    PRIMARY KEY (id),
    UNIQUE KEY uk_tenant_email (tenant_id, email) COMMENT '租户内邮箱唯一',
    UNIQUE KEY uk_tenant_local_user (tenant_id, local_user_id) COMMENT '租户内用户ID唯一',
    UNIQUE KEY uk_tenant_username (tenant_id, username) COMMENT '租户内用户名唯一',
    KEY idx_global_user_id (global_user_id),
    KEY idx_tenant_user_type (tenant_id, user_type),
    KEY idx_account_status (account_status),
    KEY idx_email_verified (email_verified),
    KEY idx_last_login_time (last_login_at),
    KEY idx_deleted (deleted),
    KEY idx_phone_hash (phone_hash)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
    COMMENT ='租户用户账户表，解决同邮箱多租户注册问题，符合美国安全合规要求';

-- =====================================================
-- 第五层：跨实例关联层 - 优化版
-- 支持独立部署间的用户关联和数据同步
-- =====================================================

CREATE TABLE dtf_cross_instance_user_mapping
(
    id                      BIGINT                                             NOT NULL AUTO_INCREMENT COMMENT '映射ID',
    global_user_id          VARCHAR(64)                                        NOT NULL COMMENT '全局用户标识',

    -- 实例映射
    source_instance_id      VARCHAR(64)                                        NOT NULL COMMENT '源实例ID',
    target_instance_id      VARCHAR(64)                                        NOT NULL COMMENT '目标实例ID',
    source_tenant_id        BIGINT                                             NOT NULL COMMENT '源租户ID',
    target_tenant_id        BIGINT                                             NOT NULL COMMENT '目标租户ID',

    -- 映射类型 - 数值编码
    mapping_type            TINYINT                                            NOT NULL COMMENT '映射类型：0-迁移,1-同步,2-联邦,3-备份',
    mapping_status          TINYINT                                            NOT NULL DEFAULT 0 COMMENT '映射状态：0-活跃,1-暂停,2-撤销,3-过期',

    -- 同步配置 (简化版)
    sync_enabled            TINYINT                                                     DEFAULT 0 COMMENT '是否启用数据同步',
    sync_config             JSON COMMENT '同步配置信息',
    last_sync_time          DATETIME COMMENT '最后同步时间',

    -- 合规控制 (美国跨州数据传输)
    cross_border_approved   TINYINT                                                     DEFAULT 0 COMMENT '跨境传输批准',
    compliance_requirements JSON COMMENT '合规要求',

    -- 映射状态
    established_at          DATETIME                                           NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '建立时间',
    expires_at              DATETIME COMMENT '过期时间',

    -- 审计字段
    create_by            VARCHAR(64) COMMENT '创建人',
    update_by            VARCHAR(64) COMMENT '更新人',
    created_at           DATETIME                                               NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at           DATETIME                                               NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    version              int                                                             default 1 not null comment '版本号',
    deleted              tinyint                                                         default 0 not null comment '删除标记',

    PRIMARY KEY (id),
    UNIQUE KEY uk_user_instance_mapping (global_user_id, source_instance_id, target_instance_id),
    KEY idx_source_instance (source_instance_id),
    KEY idx_target_instance (target_instance_id),
    KEY idx_mapping_type_status (mapping_type, mapping_status),
    KEY idx_deleted (deleted)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
    COMMENT ='跨实例用户映射表，支持独立部署间用户关联';

-- =====================================================
-- DTF权限管理表设计 - 优化版
-- =====================================================

CREATE TABLE dtf_tenant_role
(
    id                     BIGINT                                                     NOT NULL AUTO_INCREMENT COMMENT '角色ID',
    tenant_id              BIGINT                                                     NOT NULL COMMENT '租户ID',
    role_code              VARCHAR(50)                                                NOT NULL COMMENT '角色代码',
    role_name              VARCHAR(100)                                               NOT NULL COMMENT '角色名称',
    role_description       TEXT COMMENT '角色描述',

    -- 权限配置 (权限控制必需)
    permissions            JSON                                                       NOT NULL COMMENT '权限列表',
    data_scope             JSON COMMENT '数据访问范围',

    -- 角色属性
    role_level             INT                                                                 DEFAULT 1 COMMENT '角色层级',
    is_system_role         TINYINT                                                             DEFAULT 0 COMMENT '是否系统角色',

    -- 角色状态 - 数值编码
    role_status            TINYINT                                                     NOT NULL DEFAULT 0 COMMENT '角色状态：0-活跃,1-非活跃,2-已废弃',

    -- 审计字段
    create_by            VARCHAR(64) COMMENT '创建人',
    update_by            VARCHAR(64) COMMENT '更新人',
    created_at           DATETIME                                               NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at           DATETIME                                               NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    version              int                                                             default 1 not null comment '版本号',
    deleted              tinyint                                                         default 0 not null comment '删除标记',

    PRIMARY KEY (id),
    UNIQUE KEY uk_tenant_role_code (tenant_id, role_code),
    KEY idx_tenant_id (tenant_id),
    KEY idx_role_status (role_status),
    KEY idx_is_system_role (is_system_role),
    KEY idx_deleted (deleted)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
    COMMENT ='租户角色表，支持RBAC权限管理';

CREATE TABLE dtf_tenant_user_role
(
    id                     BIGINT                                             NOT NULL AUTO_INCREMENT COMMENT '用户角色关联ID',
    tenant_id              BIGINT                                             NOT NULL COMMENT '租户ID',
    user_id                BIGINT                                             NOT NULL COMMENT '用户ID',
    role_id                BIGINT                                             NOT NULL COMMENT '角色ID',

    -- 授权信息 (审计必需)
    granted_by             BIGINT COMMENT '授权人ID',
    granted_at             DATETIME                                           NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '授权时间',
    expires_at             DATETIME COMMENT '过期时间',

    -- 状态和原因 (审计追踪) - 数值编码
    role_status            TINYINT                                            NOT NULL DEFAULT 0 COMMENT '角色状态：0-活跃,1-暂停,2-过期,3-撤销',
    grant_reason           VARCHAR(200) COMMENT '授权原因',
    revoke_reason          VARCHAR(200) COMMENT '撤销原因',

    -- 审计字段
    create_by            VARCHAR(64) COMMENT '创建人',
    update_by            VARCHAR(64) COMMENT '更新人',
    created_at           DATETIME                                               NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at           DATETIME                                               NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    version              int                                                             default 1 not null comment '版本号',
    deleted              tinyint                                                         default 0 not null comment '删除标记',

    PRIMARY KEY (id),
    UNIQUE KEY uk_tenant_user_role (tenant_id, user_id, role_id),
    KEY idx_tenant_user (tenant_id, user_id),
    KEY idx_role_id (role_id),
    KEY idx_role_status (role_status),
    KEY idx_expires_at (expires_at),
    KEY idx_deleted (deleted)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
    COMMENT ='租户用户角色关联表，支持权限分配和审计';

-- =====================================================
-- DTF业务模块支撑表设计 - 优化版
-- =====================================================

-- 官方账号管理表 - 优化版
CREATE TABLE dtf_official_account
(
    id                     BIGINT                                                            NOT NULL AUTO_INCREMENT COMMENT '官方账号ID',
    account_code           VARCHAR(50)                                                       NOT NULL UNIQUE COMMENT '账号代码',
    email                  VARCHAR(100)                                                      NOT NULL UNIQUE COMMENT '官方邮箱',
    username               VARCHAR(50)                                                       NOT NULL COMMENT '用户名',

    -- 密码和安全
    password_hash          VARCHAR(255)                                                      NOT NULL COMMENT '密码哈希',
    mfa_enabled            TINYINT                                                                    DEFAULT 1 COMMENT '强制MFA',
    mfa_secret             VARCHAR(32) COMMENT 'MFA密钥',

    -- 官方账号属性 - 数值编码
    account_type           TINYINT                                                            NOT NULL COMMENT '账号类型：0-超级管理员,1-管理员,2-操作员,3-审计员,4-开发者',
    department             VARCHAR(50) COMMENT '所属部门',
    job_title              VARCHAR(50) COMMENT '职位',

    -- 权限范围
    management_permissions JSON COMMENT '管理后台权限',
    gallery_permissions    JSON COMMENT '图库管理权限',
    mall_permissions       JSON COMMENT '商城管理权限',
    community_permissions  JSON COMMENT '社区管理权限',
    audit_permissions      JSON COMMENT '审核权限',

    -- 安全配置
    ip_whitelist           JSON COMMENT 'IP白名单',
    access_time_limits     JSON COMMENT '访问时间限制',
    security_level         TINYINT                                                                    DEFAULT 1 COMMENT '安全级别：0-标准,1-高,2-关键',

    -- 账号状态 - 数值编码
    account_status         TINYINT                                                            NOT NULL DEFAULT 0 COMMENT '账号状态：0-活跃,1-暂停,2-锁定,3-已删除',
    last_login_at          DATETIME COMMENT '最后登录时间',
    last_login_ip          VARCHAR(45) COMMENT '最后登录IP',
    login_count            INT                                                                         DEFAULT 0 COMMENT '登录次数',

    -- 审计字段
    create_by            VARCHAR(64) COMMENT '创建人',
    update_by            VARCHAR(64) COMMENT '更新人',
    created_at           DATETIME                                               NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at           DATETIME                                               NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    version              int                                                             default 1 not null comment '版本号',
    deleted              tinyint                                                         default 0 not null comment '删除标记',

    PRIMARY KEY (id),
    UNIQUE KEY uk_account_code (account_code),
    UNIQUE KEY uk_email (email),
    KEY idx_account_type (account_type),
    KEY idx_department (department),
    KEY idx_account_status (account_status),
    KEY idx_deleted (deleted)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
    COMMENT ='官方账号表，独立的超级管理员体系';

-- 设备厂商代理表 - 优化版
CREATE TABLE dtf_vendor_agent
(
    id                        BIGINT                                                     NOT NULL AUTO_INCREMENT COMMENT '代理ID',
    agent_code                VARCHAR(50)                                                NOT NULL UNIQUE COMMENT '代理代码',
    agent_name                VARCHAR(100)                                               NOT NULL COMMENT '代理名称',
    vendor_tenant_id          BIGINT                                                     NOT NULL COMMENT '设备厂商租户ID',

    -- 代理属性 - 数值编码
    agent_type                TINYINT                                                    NOT NULL COMMENT '代理类型：0-直接,1-间接,2-独家,3-非独家',
    business_license          VARCHAR(100) COMMENT '营业执照',
    contact_person            VARCHAR(50) COMMENT '联系人',
    contact_email             VARCHAR(100) COMMENT '联系邮箱',
    contact_phone             VARCHAR(20) COMMENT '联系电话',

    -- 管理范围 (代理商核心配置)
    territory_scope           JSON COMMENT '管理区域范围',
    product_scope             JSON COMMENT '产品范围',
    customer_scope            JSON COMMENT '客户范围',

    -- 权限配置
    agent_permissions         JSON COMMENT '代理权限配置',
    data_access_permissions   JSON COMMENT '数据访问权限',

    -- 定制化设置
    custom_logo_url           VARCHAR(500) COMMENT '自定义Logo',
    custom_theme              JSON COMMENT '自定义主题',
    custom_domain             VARCHAR(100) COMMENT '自定义域名',

    -- 业务配置
    commission_rate           DECIMAL(5, 4)                                                      DEFAULT 0.0000 COMMENT '佣金比例',
    credit_limit              DECIMAL(15, 2) COMMENT '信用额度',

    -- 代理状态 - 数值编码
    agent_status              TINYINT                                                    NOT NULL DEFAULT 0 COMMENT '代理状态：0-活跃,1-暂停,2-终止,3-待审核',
    contract_start_date       DATE COMMENT '合同开始日期',
    contract_end_date         DATE COMMENT '合同结束日期',

    -- 审计字段
    create_by            VARCHAR(64) COMMENT '创建人',
    update_by            VARCHAR(64) COMMENT '更新人',
    created_at           DATETIME                                               NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at           DATETIME                                               NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    version              int                                                             default 1 not null comment '版本号',
    deleted              tinyint                                                         default 0 not null comment '删除标记',

    PRIMARY KEY (id),
    UNIQUE KEY uk_agent_code (agent_code),
    KEY idx_vendor_tenant (vendor_tenant_id),
    KEY idx_agent_type (agent_type),
    KEY idx_agent_status (agent_status),
    KEY idx_contract_dates (contract_start_date, contract_end_date),
    KEY idx_deleted (deleted)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
    COMMENT ='设备厂商代理表，支持多级代理体系';


-- =====================================================
-- DTF多租户用户体系核心架构表设计 v4.0 - 美国合规优化版
-- 基于美国CCPA、GDPR、SOX法规要求，优化字段设计
-- 核心优化：数值编码替代字符串枚举，敏感数据加密，性能优化
-- =====================================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =====================================================
-- 合规相关表 - 美国法规必需 (优化版)
-- =====================================================

-- 数据处理同意记录表 (CCPA必需) - 优化版
CREATE TABLE dtf_data_processing_consent
(
    id                    BIGINT                                                     NOT NULL AUTO_INCREMENT COMMENT '同意记录ID',
    user_id               BIGINT                                                     NOT NULL COMMENT '用户ID',
    tenant_id             BIGINT                                                     NOT NULL COMMENT '租户ID',
    processing_purpose    VARCHAR(100)                                               NOT NULL COMMENT '数据处理目的，CCPA必需',
    
    -- 数值编码替代字符串枚举 (数据最小化)
    consent_status        TINYINT                                                    NOT NULL DEFAULT 0 COMMENT '同意状态：0-GIVEN,1-WITHDRAWN,2-EXPIRED',
    consent_method        TINYINT                                                    NOT NULL DEFAULT 0 COMMENT '同意方式：0-WEB_FORM,1-MOBILE_APP,2-EMAIL,3-API',
    
    consent_version       VARCHAR(20) COMMENT '同意版本',
    consent_timestamp     DATETIME                                                   NOT NULL COMMENT '同意时间',
    withdrawal_timestamp  DATETIME COMMENT '撤回时间',
    expiry_timestamp      DATETIME COMMENT '过期时间',
    
    -- 隐私保护：IP地址脱敏存储
    ip_address_hash       VARCHAR(64) COMMENT 'IP地址SHA256哈希，隐私保护',
    ip_address_masked     VARCHAR(15) COMMENT 'IP地址脱敏版本(前3段)',
    
    -- 用户代理信息截断存储 (数据最小化)
    user_agent_hash       VARCHAR(64) COMMENT '用户代理SHA256哈希',
    user_agent_summary    VARCHAR(100) COMMENT '用户代理摘要(前100字符)',
    
    -- 同意证据加密存储
    consent_evidence_hash VARCHAR(64) COMMENT '同意证据哈希',
    consent_evidence_encrypted TEXT COMMENT '同意证据加密存储',

    -- 审计字段
    create_by            VARCHAR(64) COMMENT '创建人',
    update_by            VARCHAR(64) COMMENT '更新人',
    created_at           DATETIME                                               NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at           DATETIME                                               NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- 标淮字段
    version              int                                                             default 1 not null comment '版本号',
    deleted              tinyint                                                         default 0 not null comment '删除标记：0-未删除，1-已删除',

    PRIMARY KEY (id),
    KEY idx_user_tenant (user_id, tenant_id),
    KEY idx_processing_purpose (processing_purpose),
    KEY idx_consent_status (consent_status),
    KEY idx_consent_timestamp (consent_timestamp),
    KEY idx_consent_method (consent_method),
    KEY idx_ip_hash (ip_address_hash),
    KEY idx_user_agent_hash (user_agent_hash)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
    COMMENT ='数据处理同意记录表，CCPA合规必需，数值编码优化版';

-- 审计日志表 (SOX必需) - 优化版
CREATE TABLE dtf_audit_log
(
    id                    BIGINT                                     NOT NULL AUTO_INCREMENT COMMENT '审计日志ID',
    tenant_id             BIGINT COMMENT '租户ID',
    user_id               BIGINT COMMENT '用户ID',
    
    -- 操作类型和资源类型使用数值编码
    operation_type_code   TINYINT                                    NOT NULL COMMENT '操作类型编码：0-CREATE,1-UPDATE,2-DELETE,3-READ,4-LOGIN,5-LOGOUT,6-EXPORT,7-IMPORT',
    resource_type_code    TINYINT                                    NOT NULL COMMENT '资源类型编码：0-USER,1-TENANT,2-ROLE,3-PERMISSION,4-DATA,5-SYSTEM',
    
    -- 保留原始字符串用于可读性 (可选)
    operation_type        VARCHAR(50) COMMENT '操作类型描述',
    resource_type         VARCHAR(50) COMMENT '资源类型描述',
    
    resource_id           VARCHAR(100) COMMENT '资源ID',
    operation_details     JSON COMMENT '操作详情',
    
    -- 隐私保护：IP地址脱敏
    ip_address_hash       VARCHAR(64) COMMENT 'IP地址SHA256哈希',
    ip_address_masked     VARCHAR(15) COMMENT 'IP地址脱敏版本',
    
    -- 用户代理信息截断
    user_agent_hash       VARCHAR(64) COMMENT '用户代理SHA256哈希',
    user_agent_summary    VARCHAR(100) COMMENT '用户代理摘要',
    
    request_id            VARCHAR(64) COMMENT '请求ID',
    session_id            VARCHAR(64) COMMENT '会话ID',
    
    -- 操作结果使用数值编码
    operation_result      TINYINT                                    NOT NULL DEFAULT 0 COMMENT '操作结果：0-SUCCESS,1-FAILURE,2-PARTIAL',
    
    -- 错误信息加密存储
    error_message_hash    VARCHAR(64) COMMENT '错误信息哈希',
    error_message_encrypted TEXT COMMENT '错误信息加密存储',
    
    operation_timestamp   DATETIME                                   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',

    -- 审计字段
    create_by            VARCHAR(64) COMMENT '创建人',
    update_by            VARCHAR(64) COMMENT '更新人',
    created_at           DATETIME                                               NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at           DATETIME                                               NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- 标淮字段
    version              int                                                             default 1 not null comment '版本号',
    deleted              tinyint                                                         default 0 not null comment '删除标记：0-未删除，1-已删除',

    PRIMARY KEY (id),
    KEY idx_tenant_user_time (tenant_id, user_id, operation_timestamp),
    KEY idx_operation_type_time (operation_type_code, operation_timestamp),
    KEY idx_resource_type_time (resource_type_code, operation_timestamp),
    KEY idx_operation_result (operation_result),
    KEY idx_ip_hash (ip_address_hash),
    KEY idx_user_agent_hash (user_agent_hash),
    KEY idx_session_id (session_id),
    KEY idx_request_id (request_id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
    COMMENT ='审计日志表，SOX合规必需，数值编码优化版';

-- 审批工作流配置表 - 优化版
CREATE TABLE dtf_approval_workflow
(
    id                       BIGINT                                                                                                     NOT NULL AUTO_INCREMENT COMMENT '工作流ID',
    workflow_code            VARCHAR(50)                                                                                                NOT NULL UNIQUE COMMENT '工作流代码',
    workflow_name            VARCHAR(100)                                                                                               NOT NULL COMMENT '工作流名称',
    
    -- 工作流类型使用数值编码
    workflow_type_code       TINYINT                                                                                                    NOT NULL COMMENT '工作流类型编码：0-USER_REGISTRATION,1-TENANT_CREATION,2-PERMISSION_GRANT,3-DATA_EXPORT,4-ACCOUNT_DELETION',
    workflow_type            VARCHAR(50) COMMENT '工作流类型描述',
    
    tenant_id                BIGINT COMMENT '租户ID',

    -- 工作流配置 (JSON压缩存储)
    workflow_steps           JSON                                                                                                       NOT NULL COMMENT '工作流步骤配置',
    approval_rules           JSON COMMENT '审批规则',
    auto_approval_conditions JSON COMMENT '自动审批条件',

    -- 审核配置
    approver_config          JSON COMMENT '审核人员配置',
    approval_timeout_hours   INT                                                                                                                 DEFAULT 24 COMMENT '审核超时时间(小时)',

    -- 通知配置
    notification_config      JSON COMMENT '通知配置',

    -- 工作流状态使用数值编码
    workflow_status_code     TINYINT                                                                                                    NOT NULL DEFAULT 2 COMMENT '工作流状态编码：0-ACTIVE,1-INACTIVE,2-DRAFT,3-DEPRECATED',
    workflow_status          VARCHAR(20) COMMENT '工作流状态描述',
    
    workflow_version         VARCHAR(10)                                                                                                         DEFAULT '1.0' COMMENT '版本号',

    -- 审计字段
    create_by            VARCHAR(64) COMMENT '创建人',
    update_by            VARCHAR(64) COMMENT '更新人',
    created_at           DATETIME                                               NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at           DATETIME                                               NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- 标淮字段
    version              int                                                             default 1 not null comment '版本号',
    deleted              tinyint                                                         default 0 not null comment '删除标记：0-未删除，1-已删除',

    PRIMARY KEY (id),
    UNIQUE KEY uk_workflow_code (workflow_code),
    KEY idx_workflow_type (workflow_type_code),
    KEY idx_tenant_id (tenant_id),
    KEY idx_workflow_status (workflow_status_code),
    KEY idx_deleted (deleted)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
    COMMENT ='审批工作流配置表，数值编码优化版';

-- 全球地区配置表 - 支持全球化部署 (优化版)
CREATE TABLE dtf_global_region_config
(
    id                      BIGINT                                    NOT NULL AUTO_INCREMENT COMMENT '地区配置ID',
    region_code             VARCHAR(10)                               NOT NULL UNIQUE COMMENT '地区代码',
    region_name             VARCHAR(100)                              NOT NULL COMMENT '地区名称',
    region_name_en          VARCHAR(100)                              NOT NULL COMMENT '英文名称',

    -- 地理信息
    continent               VARCHAR(20)                               NOT NULL COMMENT '大洲',
    country_codes           JSON                                      NOT NULL COMMENT '包含的国家代码列表',
    timezone_list           JSON COMMENT '时区列表',

    -- 法律和合规
    data_protection_laws    JSON COMMENT '数据保护法律',
    data_residency_required TINYINT                                            DEFAULT 0 COMMENT '是否要求数据驻留',
    cross_border_restrictions JSON COMMENT '跨境数据传输限制',

    -- 业务配置使用数值编码
    market_priority_code    TINYINT                                            DEFAULT 1 COMMENT '市场优先级编码：0-PRIMARY,1-SECONDARY,2-EMERGING,3-RESTRICTED',
    market_priority         VARCHAR(20) COMMENT '市场优先级描述',
    
    launch_phase_code       TINYINT                                            DEFAULT 2 COMMENT '发布阶段编码：0-LAUNCHED,1-BETA,2-PLANNED,3-RESTRICTED',
    launch_phase            VARCHAR(20) COMMENT '发布阶段描述',

    region_status_code      TINYINT                                            DEFAULT 0 COMMENT '地区状态编码：0-ACTIVE,1-INACTIVE,2-RESTRICTED',
    region_status           VARCHAR(20) COMMENT '地区状态描述',

    -- 审计字段
    create_by            VARCHAR(64) COMMENT '创建人',
    update_by            VARCHAR(64) COMMENT '更新人',
    created_at           DATETIME                                               NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at           DATETIME                                               NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- 标淮字段
    version              int                                                             default 1 not null comment '版本号',
    deleted              tinyint                                                         default 0 not null comment '删除标记：0-未删除，1-已删除',

    PRIMARY KEY (id),
    UNIQUE KEY uk_region_code (region_code),
    KEY idx_continent (continent),
    KEY idx_market_priority (market_priority_code),
    KEY idx_launch_phase (launch_phase_code),
    KEY idx_data_residency (data_residency_required),
    KEY idx_region_status (region_status_code),
    KEY idx_deleted (deleted)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
    COMMENT ='全球地区配置表，支持全球化合规和本地化，数值编码优化版';

-- 数据保护策略表 - 合规管理 (优化版)
CREATE TABLE dtf_data_protection_policy
(
    id                    BIGINT                                                                                          NOT NULL AUTO_INCREMENT COMMENT '策略ID',
    policy_code           VARCHAR(50)                                                                                     NOT NULL UNIQUE COMMENT '策略代码',
    policy_name           VARCHAR(100)                                                                                    NOT NULL COMMENT '策略名称',
    
    -- 策略类型使用数值编码
    policy_type_code      TINYINT                                                                                          NOT NULL COMMENT '策略类型编码：0-DATA_RETENTION,1-DATA_ENCRYPTION,2-ACCESS_CONTROL,3-AUDIT_LOGGING,4-CROSS_BORDER',
    policy_type           VARCHAR(30) COMMENT '策略类型描述',
    
    applicable_regions    JSON COMMENT '适用地区',
    applicable_data_types JSON COMMENT '适用数据类型',

    -- 策略内容
    policy_rules          JSON                                                                                            NOT NULL COMMENT '策略规则',
    implementation_guide  TEXT COMMENT '实施指南',

    -- 合规要求
    legal_basis           VARCHAR(200) COMMENT '法律依据',
    compliance_standards  JSON COMMENT '合规标准',

    -- 访问控制
    access_restrictions   JSON COMMENT '访问限制',
    encryption_required   TINYINT                                                                                                  DEFAULT 1 COMMENT '是否要求加密',
    audit_logging         TINYINT                                                                                                  DEFAULT 1 COMMENT '是否记录审计日志',

    -- 策略状态使用数值编码
    policy_status_code    TINYINT                                                                                          NOT NULL DEFAULT 2 COMMENT '策略状态编码：0-ACTIVE,1-INACTIVE,2-DRAFT',
    policy_status         VARCHAR(20) COMMENT '策略状态描述',
    
    effective_date        DATE                                                                                    NOT NULL COMMENT '生效日期',
    expiry_date           DATE COMMENT '失效日期',

    -- 审计字段
    create_by            VARCHAR(64) COMMENT '创建人',
    update_by            VARCHAR(64) COMMENT '更新人',
    created_at           DATETIME                                               NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at           DATETIME                                               NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- 标淮字段
    version              int                                                             default 1 not null comment '版本号',
    deleted              tinyint                                                         default 0 not null comment '删除标记：0-未删除，1-已删除',

    PRIMARY KEY (id),
    UNIQUE KEY uk_policy_code (policy_code),
    KEY idx_policy_type (policy_type_code),
    KEY idx_policy_status (policy_status_code),
    KEY idx_effective_date (effective_date),
    KEY idx_deleted (deleted)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
    COMMENT ='数据保护策略表，支持合规管理，数值编码优化版';

-- =====================================================
-- 数值编码映射表 - 便于维护和查询
-- =====================================================

-- 同意状态映射表
CREATE TABLE dtf_consent_status_mapping
(
    status_code           TINYINT                                    NOT NULL PRIMARY KEY COMMENT '状态编码',
    status_name           VARCHAR(20)                                NOT NULL COMMENT '状态名称',
    status_description    VARCHAR(100) COMMENT '状态描述',
    created_at           DATETIME                                   NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
    COMMENT ='同意状态编码映射表';

-- 操作类型映射表
CREATE TABLE dtf_operation_type_mapping
(
    type_code             TINYINT                                    NOT NULL PRIMARY KEY COMMENT '类型编码',
    type_name             VARCHAR(20)                                NOT NULL COMMENT '类型名称',
    type_description      VARCHAR(100) COMMENT '类型描述',
    created_at           DATETIME                                   NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
    COMMENT ='操作类型编码映射表';

-- 资源类型映射表
CREATE TABLE dtf_resource_type_mapping
(
    type_code             TINYINT                                    NOT NULL PRIMARY KEY COMMENT '类型编码',
    type_name             VARCHAR(20)                                NOT NULL COMMENT '类型名称',
    type_description      VARCHAR(100) COMMENT '类型描述',
    created_at           DATETIME                                   NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
    COMMENT ='资源类型编码映射表';

-- =====================================================
-- 初始化映射数据
-- =====================================================

-- 同意状态映射数据
INSERT INTO dtf_consent_status_mapping (status_code, status_name, status_description) VALUES
(0, 'GIVEN', '已同意'),
(1, 'WITHDRAWN', '已撤回'),
(2, 'EXPIRED', '已过期');

-- 操作类型映射数据
INSERT INTO dtf_operation_type_mapping (type_code, type_name, type_description) VALUES
(0, 'CREATE', '创建'),
(1, 'UPDATE', '更新'),
(2, 'DELETE', '删除'),
(3, 'READ', '读取'),
(4, 'LOGIN', '登录'),
(5, 'LOGOUT', '登出'),
(6, 'EXPORT', '导出'),
(7, 'IMPORT', '导入');

-- 资源类型映射数据
INSERT INTO dtf_resource_type_mapping (type_code, type_name, type_description) VALUES
(0, 'USER', '用户'),
(1, 'TENANT', '租户'),
(2, 'ROLE', '角色'),
(3, 'PERMISSION', '权限'),
(4, 'DATA', '数据'),
(5, 'SYSTEM', '系统');

-- =====================================================
-- 性能优化视图 - 提供可读性
-- =====================================================

-- 同意记录可读视图
CREATE VIEW v_data_processing_consent_readable AS
SELECT 
    c.*,
    cs.status_name as consent_status_name,
    cm.method_name as consent_method_name
FROM dtf_data_processing_consent c
LEFT JOIN dtf_consent_status_mapping cs ON c.consent_status = cs.status_code
LEFT JOIN (
    SELECT 0 as method_code, 'WEB_FORM' as method_name UNION ALL
    SELECT 1, 'MOBILE_APP' UNION ALL
    SELECT 2, 'EMAIL' UNION ALL
    SELECT 3, 'API'
) cm ON c.consent_method = cm.method_code;

-- 审计日志可读视图
CREATE VIEW v_audit_log_readable AS
SELECT 
    a.*,
    ot.type_name as operation_type_name,
    rt.type_name as resource_type_name,
    CASE a.operation_result 
        WHEN 0 THEN 'SUCCESS'
        WHEN 1 THEN 'FAILURE'
        WHEN 2 THEN 'PARTIAL'
    END as operation_result_name
FROM dtf_audit_log a
LEFT JOIN dtf_operation_type_mapping ot ON a.operation_type_code = ot.type_code
LEFT JOIN dtf_resource_type_mapping rt ON a.resource_type_code = rt.type_code;

SET FOREIGN_KEY_CHECKS = 1;

-- =====================================================
-- 优化说明
-- =====================================================

/*
优化要点：

1. 数值编码替代字符串枚举：
   - 同意状态：0-GIVEN, 1-WITHDRAWN, 2-EXPIRED
   - 操作类型：0-CREATE, 1-UPDATE, 2-DELETE, 3-READ, 4-LOGIN, 5-LOGOUT, 6-EXPORT, 7-IMPORT
   - 资源类型：0-USER, 1-TENANT, 2-ROLE, 3-PERMISSION, 4-DATA, 5-SYSTEM
   - 工作流状态：0-ACTIVE, 1-INACTIVE, 2-DRAFT, 3-DEPRECATED

2. 隐私保护措施：
   - IP地址哈希存储和脱敏
   - 用户代理信息哈希和截断
   - 敏感信息加密存储
   - 错误信息加密存储

3. 性能优化：
   - 数值编码字段索引优化
   - 复合索引设计
   - JSON字段压缩存储
   - 映射表提供可读性

4. 合规性增强：
   - CCPA数据最小化原则
   - GDPR隐私保护要求
   - SOX审计完整性
   - 数据保留策略支持

5. 可维护性保障：
   - 映射表便于维护
   - 视图提供可读性
   - 向后兼容设计
   - 详细注释说明
*/


-- =====================================================
-- 索引优化建议
-- =====================================================

-- 为JSON字段添加生成列索引（可选，高频查询时使用）
-- 示例：为tenant表的feature_config添加功能查询优化
-- ALTER TABLE dtf_tenant
--     ADD COLUMN gallery_enabled TINYINT
--         AS (JSON_EXTRACT(feature_config, '$.gallery_enabled')) STORED,
--     ADD INDEX idx_gallery_enabled (gallery_enabled);

-- 示例：为vendor_agent表的territory_scope添加地区查询优化
-- ALTER TABLE dtf_vendor_agent
--     ADD COLUMN territory_region VARCHAR(50)
--         AS (JSON_UNQUOTE(JSON_EXTRACT(territory_scope, '$.region'))) STORED,
--     ADD INDEX idx_territory_region (territory_region);

-- =====================================================
-- 表结构验证查询
-- =====================================================

-- 验证所有表是否创建成功
SELECT
    TABLE_NAME as '表名',
    TABLE_COMMENT as '表注释',
    TABLE_ROWS as '预估行数'
FROM information_schema.TABLES
WHERE TABLE_SCHEMA = DATABASE()
  AND TABLE_NAME LIKE 'dtf_%'
ORDER BY TABLE_NAME;

-- 验证索引创建情况
SELECT
    TABLE_NAME as '表名',
    INDEX_NAME as '索引名',
    COLUMN_NAME as '列名',
    INDEX_TYPE as '索引类型'
FROM information_schema.STATISTICS
WHERE TABLE_SCHEMA = DATABASE()
  AND TABLE_NAME LIKE 'dtf_%'
ORDER BY TABLE_NAME, INDEX_NAME;
