-- =====================================================
-- DTF多租户用户体系核心架构表设计 v3.0 - 美国合规MVP版
-- 基于美国CCPA、SOX法规要求，优化字段设计
-- =====================================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =====================================================
-- 第一层：全局身份管理层 - 18个核心字段
-- 解决跨租户用户识别和隐私保护，符合CCPA要求
-- =====================================================

CREATE TABLE dtf_global_identity
(
    id                   BIGINT                                                 NOT NULL AUTO_INCREMENT COMMENT '全局身份ID',
    global_user_id       VARCHAR(64)                                            NOT NULL UNIQUE COMMENT '全局唯一用户标识(UUID)',

    -- 隐私保护设计 (CCPA必需)
    email_hash           VARCHAR(64)                                            NOT NULL COMMENT '邮箱SHA256哈希，用于关联检测',
    phone_hash           VARCHAR(64) COMMENT '手机号哈希',
    identity_fingerprint VARCHAR(128) COMMENT '身份指纹(设备+行为特征)，防欺诈必需',

    -- 身份属性 (业务必需)
    identity_type        ENUM ('CONSUMER', 'VENDOR_ADMIN', 'AGENT', 'OFFICIAL') NOT NULL DEFAULT 'CONSUMER',
    verification_level   TINYINT                                                NOT NULL DEFAULT 0 COMMENT '0-未验证,1-邮箱验证,2-手机验证,3-实名验证',
    risk_score           DECIMAL(3, 2)                                                   DEFAULT 0.00 COMMENT '风险评分0-1，金融业务必需',

    -- 全局状态
    global_status        ENUM ('ACTIVE', 'SUSPENDED', 'DELETED')                NOT NULL DEFAULT 'ACTIVE',
    suspension_reason    VARCHAR(200) COMMENT '暂停原因',

    -- 合规字段 (CCPA/GDPR必需)
    gdpr_consent_version VARCHAR(20) COMMENT 'GDPR同意版本，跨境用户可能需要',
    data_retention_until DATE COMMENT '数据保留截止日期，CCPA自动删除必需',
    last_privacy_policy_accepted_at DATETIME COMMENT '最后接受隐私政策时间，合规审计必需',

    -- 审计字段 (SOX必需)
    -- 审计字段(继承BaseEntity)
    create_by            VARCHAR(64) COMMENT '创建人',
    update_by            VARCHAR(64) COMMENT '更新人',
    created_at           DATETIME                                               NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at           DATETIME                                               NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- 标淮字段
    version              int                                                             default 1 not null comment '版本号',
    deleted              tinyint                                                         default 0 not null comment '删除标记：0-未删除，1-已删除',


    PRIMARY KEY (id),
    UNIQUE KEY uk_global_user_id (global_user_id),
    KEY idx_email_hash (email_hash),
    KEY idx_identity_type_status (identity_type, global_status),
    KEY idx_verification_level (verification_level),
    KEY idx_created_at (created_at),
    KEY idx_deleted (deleted)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
    COMMENT ='全局用户身份表，支持跨租户用户识别和隐私保护，符合CCPA要求';

-- =====================================================
-- 第二层：部署实例管理层 - 15个核心字段
-- 支持AWS部署和美国合规要求
-- =====================================================

CREATE TABLE dtf_deployment_instance
(
    id                        BIGINT                                                     NOT NULL AUTO_INCREMENT COMMENT '部署实例ID',
    instance_id               VARCHAR(64)                                                NOT NULL UNIQUE COMMENT '实例唯一标识',
    instance_name             VARCHAR(100)                                               NOT NULL COMMENT '实例名称',
    deployment_type           ENUM ('SAAS', 'PRIVATE_CLOUD', 'HYBRID', 'ON_PREMISE')    NOT NULL DEFAULT 'SAAS',

    -- 网络配置 (AWS部署必需)
    primary_domain            VARCHAR(100)                                               NOT NULL COMMENT '主域名',
    api_gateway_url           VARCHAR(200) COMMENT 'API网关地址',
    cdn_domain                VARCHAR(100) COMMENT 'CDN域名',

    -- 基础设施配置 (运维必需，单人开发需要灵活配置)
    database_config           JSON COMMENT '数据库配置信息',
    storage_config            JSON COMMENT '存储配置信息(AWS S3等)',
    cache_config              JSON COMMENT '缓存配置信息(Redis等)',

    -- 地理位置和合规 (美国法规必需)
    region_code               VARCHAR(10)                                                NOT NULL COMMENT '部署区域：US-EAST-1/US-WEST-2等',
    data_residency_rules      JSON COMMENT '数据驻留规则，州法律要求',
    compliance_certifications JSON COMMENT '合规认证列表(SOX/PCI/HIPAA等)',

    -- 实例状态
    instance_status           ENUM ('RUNNING', 'STOPPED', 'MAINTENANCE', 'DEPRECATED')  NOT NULL DEFAULT 'RUNNING',

    -- 审计字段(继承BaseEntity)
    create_by            VARCHAR(64) COMMENT '创建人',
    update_by            VARCHAR(64) COMMENT '更新人',
    created_at           DATETIME                                               NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at           DATETIME                                               NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- 标淮字段
    version              int                                                             default 1 not null comment '版本号',
    deleted              tinyint                                                         default 0 not null comment '删除标记：0-未删除，1-已删除',


    PRIMARY KEY (id),
    UNIQUE KEY uk_instance_id (instance_id),
    KEY idx_deployment_type (deployment_type),
    KEY idx_region_code (region_code),
    KEY idx_instance_status (instance_status)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
    COMMENT ='部署实例表，支持AWS部署和美国合规要求';

-- =====================================================
-- 第三层：租户管理层 - 25个核心字段
-- 支持多层级租户和代理商体系，符合商业化需求
-- =====================================================

CREATE TABLE dtf_tenant
(
    id                       BIGINT                                                                 NOT NULL AUTO_INCREMENT COMMENT '租户ID',
    tenant_code              VARCHAR(50)                                                            NOT NULL UNIQUE COMMENT '租户代码',
    tenant_name              VARCHAR(100)                                                           NOT NULL COMMENT '租户名称',
    tenant_name_en           VARCHAR(100) COMMENT '英文名称',
    tenant_type              ENUM ('OFFICIAL', 'DEVICE_VENDOR', 'AGENT')                           NOT NULL DEFAULT 'DEVICE_VENDOR',

    -- 层级关系 (代理商体系必需，删除后难以重建)
    parent_tenant_id         BIGINT COMMENT '父租户ID',
    tenant_hierarchy         VARCHAR(200) COMMENT '租户层级路径，如/1/2/3',
    hierarchy_level          INT                                                                             DEFAULT 0 COMMENT '层级深度',

    -- 部署配置
    deployment_instance_id   BIGINT                                                                 NOT NULL COMMENT '部署实例ID',
    isolation_level          ENUM ('DATABASE', 'SCHEMA', 'TABLE')                                           DEFAULT 'SCHEMA' COMMENT '隔离级别',

    -- 品牌定制 (设备厂商核心需求，竞争优势)
    brand_config             JSON COMMENT '品牌定制配置',
    theme_config             JSON COMMENT '主题配置',
    logo_url                 VARCHAR(500) COMMENT 'Logo URL',
    custom_domain            VARCHAR(100) COMMENT '自定义域名',

    -- 功能配置 (产品差异化必需)
    feature_config           JSON COMMENT '功能开关配置',
    module_permissions       JSON COMMENT '模块权限配置',
    api_rate_limits          JSON COMMENT 'API限流配置',

    -- 资源配额 (SaaS模式必需)
    user_quota               INT                                                                             DEFAULT 100 COMMENT '用户配额',
    storage_quota_gb         INT                                                                             DEFAULT 10 COMMENT '存储配额(GB)',
    api_quota_daily          INT                                                                             DEFAULT 10000 COMMENT '每日API调用配额',

    -- 计费配置 (商业化核心，后期修改成本高)
    billing_model            ENUM ('FREE', 'SUBSCRIPTION', 'PAY_AS_GO', 'ENTERPRISE')                      DEFAULT 'FREE',
    billing_config           JSON COMMENT '计费规则配置',
    subscription_plan        VARCHAR(50) COMMENT '订阅计划',

    -- 合规配置 (美国法规硬性要求)
    data_processing_purposes JSON COMMENT '数据处理目的，CCPA必需',
    retention_policies       JSON COMMENT '数据保留策略',
    privacy_settings         JSON COMMENT '隐私设置配置',

    -- 租户状态
    tenant_status            ENUM ('ACTIVE', 'SUSPENDED', 'EXPIRED', 'DELETED')                    NOT NULL DEFAULT 'ACTIVE',

    -- 审计字段(继承BaseEntity)
    create_by            VARCHAR(64) COMMENT '创建人',
    update_by            VARCHAR(64) COMMENT '更新人',
    created_at           DATETIME                                               NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at           DATETIME                                               NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- 标淮字段
    version              int                                                             default 1 not null comment '版本号',
    deleted              tinyint                                                         default 0 not null comment '删除标记：0-未删除，1-已删除',


    PRIMARY KEY (id),
    UNIQUE KEY uk_tenant_code (tenant_code),
    KEY idx_tenant_type (tenant_type),
    KEY idx_deployment_instance (deployment_instance_id),
    KEY idx_parent_tenant (parent_tenant_id),
    KEY idx_tenant_status (tenant_status),
    KEY idx_hierarchy_level (hierarchy_level)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
    COMMENT ='租户表，支持多层级管理和代理商体系';

-- =====================================================
-- 第四层：租户用户账户层 - 30个核心字段
-- 解决同邮箱多租户注册问题，符合美国安全和合规要求
-- =====================================================

CREATE TABLE dtf_tenant_user_account
(
    id                      BIGINT                                                         NOT NULL AUTO_INCREMENT COMMENT '用户账户ID',
    tenant_id               BIGINT                                                         NOT NULL COMMENT '租户ID',
    global_user_id          VARCHAR(64)                                                    NOT NULL COMMENT '全局用户标识',
    local_user_id           VARCHAR(64) COMMENT '租户内用户ID',
    username                VARCHAR(50) COMMENT '用户名',
    email                   VARCHAR(100)                                                   NOT NULL COMMENT '邮箱',

    -- 密码和安全 (美国安全要求，网络攻击频繁)
    password_hash           VARCHAR(255)                                                   NOT NULL COMMENT '密码哈希',
    password_salt           VARCHAR(32) COMMENT '密码盐值，安全加固',
    password_updated_at     DATETIME COMMENT '密码更新时间，密码策略必需',
    failed_login_attempts   TINYINT                                                                 DEFAULT 0 COMMENT '登录失败次数',
    locked_until            DATETIME COMMENT '锁定截止时间',

    -- 多因素认证 (企业客户基本安全要求)
    mfa_enabled             TINYINT                                                                 DEFAULT 0 COMMENT '是否启用MFA',
    mfa_secret              VARCHAR(32) COMMENT 'MFA密钥',
    backup_codes            JSON COMMENT '备用验证码',

    -- 用户属性
    user_type               ENUM ('CONSUMER', 'VENDOR_ADMIN', 'AGENT', 'SUPPORT', 'DEVELOPER')     DEFAULT 'CONSUMER',
    user_level              ENUM ('BASIC', 'PREMIUM', 'VIP', 'ENTERPRISE')                         DEFAULT 'BASIC',

    -- 个人信息 (实名认证、年龄验证等可能的合规要求)
    nickname                VARCHAR(50) COMMENT '昵称',
    real_name               VARCHAR(50) COMMENT '真实姓名',
    avatar_url              VARCHAR(500) COMMENT '头像URL',
    gender                  ENUM ('MALE', 'FEMALE', 'OTHER', 'UNKNOWN')                            DEFAULT 'UNKNOWN',
    birth_date              DATE COMMENT '出生日期，年龄验证可能需要',

    -- 联系方式
    phone                   VARCHAR(20) COMMENT '手机号',
    phone_verified          TINYINT                                                                 DEFAULT 0 COMMENT '手机验证状态',
    address                 JSON COMMENT '地址信息，可能需要',

    -- 账户状态
    account_status          ENUM ('PENDING', 'ACTIVE', 'SUSPENDED', 'DELETED')                     DEFAULT 'PENDING',
    suspension_reason       VARCHAR(200) COMMENT '暂停原因',
    activation_token        VARCHAR(64) COMMENT '激活令牌',
    activation_expires_at   DATETIME COMMENT '激活令牌过期时间',

    -- 业务属性 (商业化必需)
    subscription_type       VARCHAR(20)                                                             DEFAULT 'FREE' COMMENT '订阅类型',
    subscription_expires_at DATETIME COMMENT '订阅过期时间',

    -- 登录信息 (安全审计必需)
    last_login_at           DATETIME COMMENT '最后登录时间',
    last_login_ip           VARCHAR(45) COMMENT '最后登录IP，安全审计必需',
    login_count             INT                                                                     DEFAULT 0 COMMENT '登录次数',

    -- 地理和本地化 (美国市场必需，州法律适用)
    country_code            VARCHAR(10) COMMENT '国家代码',
    region_code             VARCHAR(10) COMMENT '地区代码，州法律适用',
    language                VARCHAR(10)                                                             DEFAULT 'en' COMMENT '语言偏好',
    timezone                VARCHAR(50) COMMENT '时区，时区处理必需',

    -- 合规字段 (CCPA硬性要求)
    gdpr_consent            TINYINT                                                                 DEFAULT 0 COMMENT 'GDPR同意状态',
    marketing_consent       TINYINT                                                                 DEFAULT 0 COMMENT '营销同意状态',
    data_processing_consent JSON COMMENT '数据处理同意记录，CCPA必需',

    email_verified          TINYINT                                                                 DEFAULT 0 COMMENT '邮箱验证状态',

    -- 审计字段(继承BaseEntity)
    create_by            VARCHAR(64) COMMENT '创建人',
    update_by            VARCHAR(64) COMMENT '更新人',
    created_at           DATETIME                                               NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at           DATETIME                                               NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- 标淮字段
    version              int                                                             default 1 not null comment '版本号',
    deleted              tinyint                                                         default 0 not null comment '删除标记：0-未删除，1-已删除',

    PRIMARY KEY (id),
    UNIQUE KEY uk_tenant_email (tenant_id, email) COMMENT '租户内邮箱唯一',
    UNIQUE KEY uk_tenant_local_user (tenant_id, local_user_id) COMMENT '租户内用户ID唯一',
    UNIQUE KEY uk_tenant_username (tenant_id, username) COMMENT '租户内用户名唯一',
    KEY idx_global_user_id (global_user_id),
    KEY idx_tenant_user_type (tenant_id, user_type),
    KEY idx_account_status (account_status),
    KEY idx_email_verified (email_verified),
    KEY idx_last_login_time (last_login_at),
    KEY idx_deleted (deleted)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
    COMMENT ='租户用户账户表，解决同邮箱多租户注册问题，符合美国安全合规要求';

-- =====================================================
-- 第五层：跨实例关联层 - 保留核心字段
-- 支持独立部署间的用户关联和数据同步
-- =====================================================

CREATE TABLE dtf_cross_instance_user_mapping
(
    id                      BIGINT                                             NOT NULL AUTO_INCREMENT COMMENT '映射ID',
    global_user_id          VARCHAR(64)                                        NOT NULL COMMENT '全局用户标识',

    -- 实例映射
    source_instance_id      VARCHAR(64)                                        NOT NULL COMMENT '源实例ID',
    target_instance_id      VARCHAR(64)                                        NOT NULL COMMENT '目标实例ID',
    source_tenant_id        BIGINT                                             NOT NULL COMMENT '源租户ID',
    target_tenant_id        BIGINT                                             NOT NULL COMMENT '目标租户ID',

    -- 映射类型
    mapping_type            ENUM ('MIGRATION', 'SYNC', 'FEDERATION', 'BACKUP') NOT NULL COMMENT '映射类型',
    mapping_status          ENUM ('ACTIVE', 'SUSPENDED', 'REVOKED', 'EXPIRED') NOT NULL DEFAULT 'ACTIVE',

    -- 同步配置 (简化版)
    sync_enabled            TINYINT                                                     DEFAULT 0 COMMENT '是否启用数据同步',
    sync_config             JSON COMMENT '同步配置信息',
    last_sync_time          DATETIME COMMENT '最后同步时间',

    -- 合规控制 (美国跨州数据传输)
    cross_border_approved   TINYINT                                                     DEFAULT 0 COMMENT '跨境传输批准',
    compliance_requirements JSON COMMENT '合规要求',

    -- 映射状态
    established_at          DATETIME                                           NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '建立时间',
    expires_at              DATETIME COMMENT '过期时间',

    -- 审计字段(继承BaseEntity)
    create_by            VARCHAR(64) COMMENT '创建人',
    update_by            VARCHAR(64) COMMENT '更新人',
    created_at           DATETIME                                               NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at           DATETIME                                               NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- 标淮字段
    version              int                                                             default 1 not null comment '版本号',
    deleted              tinyint                                                         default 0 not null comment '删除标记：0-未删除，1-已删除',


    PRIMARY KEY (id),
    UNIQUE KEY uk_user_instance_mapping (global_user_id, source_instance_id, target_instance_id),
    KEY idx_source_instance (source_instance_id),
    KEY idx_target_instance (target_instance_id),
    KEY idx_mapping_type_status (mapping_type, mapping_status),
    KEY idx_deleted (deleted)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
    COMMENT ='跨实例用户映射表，支持独立部署间用户关联';

-- =====================================================
-- DTF权限管理表设计 - 12个核心字段
-- =====================================================

CREATE TABLE dtf_tenant_role
(
    id                     BIGINT                                                     NOT NULL AUTO_INCREMENT COMMENT '角色ID',
    tenant_id              BIGINT                                                     NOT NULL COMMENT '租户ID',
    role_code              VARCHAR(50)                                                NOT NULL COMMENT '角色代码',
    role_name              VARCHAR(100)                                               NOT NULL COMMENT '角色名称',
    role_description       TEXT COMMENT '角色描述',

    -- 权限配置 (权限控制必需)
    permissions            JSON                                                       NOT NULL COMMENT '权限列表',
    data_scope             JSON COMMENT '数据访问范围',

    -- 角色属性
    role_level             INT                                                                 DEFAULT 1 COMMENT '角色层级',
    is_system_role         TINYINT                                                             DEFAULT 0 COMMENT '是否系统角色',

    -- 角色状态
    role_status            ENUM ('ACTIVE', 'INACTIVE', 'DEPRECATED')                 NOT NULL DEFAULT 'ACTIVE',

    -- 审计字段(继承BaseEntity)
    create_by            VARCHAR(64) COMMENT '创建人',
    update_by            VARCHAR(64) COMMENT '更新人',
    created_at           DATETIME                                               NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at           DATETIME                                               NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- 标淮字段
    version              int                                                             default 1 not null comment '版本号',
    deleted              tinyint                                                         default 0 not null comment '删除标记：0-未删除，1-已删除',

    PRIMARY KEY (id),
    UNIQUE KEY uk_tenant_role_code (tenant_id, role_code),
    KEY idx_tenant_id (tenant_id),
    KEY idx_role_status (role_status),
    KEY idx_is_system_role (is_system_role),
    KEY idx_deleted (deleted)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
    COMMENT ='租户角色表，支持RBAC权限管理';

CREATE TABLE dtf_tenant_user_role
(
    id                     BIGINT                                             NOT NULL AUTO_INCREMENT COMMENT '用户角色关联ID',
    tenant_id              BIGINT                                             NOT NULL COMMENT '租户ID',
    user_id                BIGINT                                             NOT NULL COMMENT '用户ID',
    role_id                BIGINT                                             NOT NULL COMMENT '角色ID',

    -- 授权信息 (审计必需)
    granted_by             BIGINT COMMENT '授权人ID',
    granted_at             DATETIME                                           NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '授权时间',
    expires_at             DATETIME COMMENT '过期时间',

    -- 状态和原因 (审计追踪)
    role_status            ENUM ('ACTIVE', 'SUSPENDED', 'EXPIRED', 'REVOKED') NOT NULL DEFAULT 'ACTIVE',
    grant_reason           VARCHAR(200) COMMENT '授权原因',
    revoke_reason          VARCHAR(200) COMMENT '撤销原因',

    -- 审计字段(继承BaseEntity)
    create_by            VARCHAR(64) COMMENT '创建人',
    update_by            VARCHAR(64) COMMENT '更新人',
    created_at           DATETIME                                               NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at           DATETIME                                               NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- 标淮字段
    version              int                                                             default 1 not null comment '版本号',
    deleted              tinyint                                                         default 0 not null comment '删除标记：0-未删除，1-已删除',

    PRIMARY KEY (id),
    UNIQUE KEY uk_tenant_user_role (tenant_id, user_id, role_id),
    KEY idx_tenant_user (tenant_id, user_id),
    KEY idx_role_id (role_id),
    KEY idx_role_status (role_status),
    KEY idx_expires_at (expires_at),
    KEY idx_deleted (deleted)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
    COMMENT ='租户用户角色关联表，支持权限分配和审计';

-- =====================================================
-- 合规相关表 - 美国法规必需
-- =====================================================

-- 数据处理同意记录表 (CCPA必需)
CREATE TABLE dtf_data_processing_consent
(
    id                    BIGINT                                                     NOT NULL AUTO_INCREMENT COMMENT '同意记录ID',
    user_id               BIGINT                                                     NOT NULL COMMENT '用户ID',
    tenant_id             BIGINT                                                     NOT NULL COMMENT '租户ID',
    processing_purpose    VARCHAR(100)                                               NOT NULL COMMENT '数据处理目的，CCPA必需',
    consent_status        ENUM ('GIVEN', 'WITHDRAWN', 'EXPIRED')                    NOT NULL COMMENT '同意状态',
    consent_method        ENUM ('WEB_FORM', 'MOBILE_APP', 'EMAIL', 'API')           NOT NULL COMMENT '同意方式',
    consent_version       VARCHAR(20) COMMENT '同意版本',
    consent_timestamp     DATETIME                                                   NOT NULL COMMENT '同意时间',
    withdrawal_timestamp  DATETIME COMMENT '撤回时间',
    expiry_timestamp      DATETIME COMMENT '过期时间',
    ip_address            VARCHAR(45) COMMENT 'IP地址，审计必需',
    user_agent            TEXT COMMENT '用户代理，审计必需',
    consent_evidence      JSON COMMENT '同意证据，法律证据',

    -- 审计字段(继承BaseEntity)
    create_by            VARCHAR(64) COMMENT '创建人',
    update_by            VARCHAR(64) COMMENT '更新人',
    created_at           DATETIME                                               NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at           DATETIME                                               NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- 标淮字段
    version              int                                                             default 1 not null comment '版本号',
    deleted              tinyint                                                         default 0 not null comment '删除标记：0-未删除，1-已删除',

    PRIMARY KEY (id),
    KEY idx_user_tenant (user_id, tenant_id),
    KEY idx_processing_purpose (processing_purpose),
    KEY idx_consent_status (consent_status),
    KEY idx_consent_timestamp (consent_timestamp)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
    COMMENT ='数据处理同意记录表，CCPA合规必需';

-- 审计日志表 (SOX必需)
CREATE TABLE dtf_audit_log
(
    id                    BIGINT                                     NOT NULL AUTO_INCREMENT COMMENT '审计日志ID',
    tenant_id             BIGINT COMMENT '租户ID',
    user_id               BIGINT COMMENT '用户ID',
    operation_type        VARCHAR(50)                                NOT NULL COMMENT '操作类型',
    resource_type         VARCHAR(50)                                NOT NULL COMMENT '资源类型',
    resource_id           VARCHAR(100) COMMENT '资源ID',
    operation_details     JSON COMMENT '操作详情',
    ip_address            VARCHAR(45) COMMENT 'IP地址',
    user_agent            TEXT COMMENT '用户代理',
    request_id            VARCHAR(64) COMMENT '请求ID',
    session_id            VARCHAR(64) COMMENT '会话ID',
    operation_result      ENUM ('SUCCESS', 'FAILURE', 'PARTIAL')     DEFAULT 'SUCCESS' COMMENT '操作结果',
    error_message         TEXT COMMENT '错误信息',
    operation_timestamp   DATETIME                                   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',

    -- 审计字段(继承BaseEntity)
    create_by            VARCHAR(64) COMMENT '创建人',
    update_by            VARCHAR(64) COMMENT '更新人',
    created_at           DATETIME                                               NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at           DATETIME                                               NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- 标淮字段
    version              int                                                             default 1 not null comment '版本号',
    deleted              tinyint                                                         default 0 not null comment '删除标记：0-未删除，1-已删除',


    PRIMARY KEY (id),
    KEY idx_tenant_user_time (tenant_id, user_id, operation_timestamp),
    KEY idx_operation_type_time (operation_type, operation_timestamp),
    KEY idx_resource_type_time (resource_type, operation_timestamp),
    KEY idx_operation_result (operation_result)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
    COMMENT ='审计日志表，SOX合规必需';

-- =====================================================
-- 工作流和审批表 - 保留核心功能
-- =====================================================

CREATE TABLE dtf_approval_workflow
(
    id                       BIGINT                                                                                                     NOT NULL AUTO_INCREMENT COMMENT '工作流ID',
    workflow_code            VARCHAR(50)                                                                                                NOT NULL UNIQUE COMMENT '工作流代码',
    workflow_name            VARCHAR(100)                                                                                               NOT NULL COMMENT '工作流名称',
    workflow_type            ENUM ('USER_REGISTRATION', 'TENANT_CREATION', 'PERMISSION_GRANT', 'DATA_EXPORT', 'ACCOUNT_DELETION')     NOT NULL COMMENT '工作流类型',
    tenant_id                BIGINT COMMENT '租户ID',

    -- 工作流配置
    workflow_steps           JSON                                                                                                       NOT NULL COMMENT '工作流步骤配置',
    approval_rules           JSON COMMENT '审批规则',
    auto_approval_conditions JSON COMMENT '自动审批条件',

    -- 审核配置
    approver_config          JSON COMMENT '审核人员配置',
    approval_timeout_hours   INT                                                                                                                 DEFAULT 24 COMMENT '审核超时时间(小时)',

    -- 通知配置
    notification_config      JSON COMMENT '通知配置',

    -- 工作流状态
    workflow_status          ENUM ('ACTIVE', 'INACTIVE', 'DRAFT', 'DEPRECATED')                                                        NOT NULL DEFAULT 'DRAFT',
    workflow_version         VARCHAR(10)                                                                                                         DEFAULT '1.0' COMMENT '版本号',

    -- 审计字段(继承BaseEntity)
    create_by            VARCHAR(64) COMMENT '创建人',
    update_by            VARCHAR(64) COMMENT '更新人',
    created_at           DATETIME                                               NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at           DATETIME                                               NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- 标淮字段
    version              int                                                             default 1 not null comment '版本号',
    deleted              tinyint                                                         default 0 not null comment '删除标记：0-未删除，1-已删除',

    PRIMARY KEY (id),
    UNIQUE KEY uk_workflow_code (workflow_code),
    KEY idx_workflow_type (workflow_type),
    KEY idx_tenant_id (tenant_id),
    KEY idx_workflow_status (workflow_status),
    KEY idx_deleted (deleted)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
    COMMENT ='审批工作流配置表';

-- 全球地区配置表 - 支持全球化部署
CREATE TABLE dtf_global_region_config
(
    id                      BIGINT                                    NOT NULL AUTO_INCREMENT COMMENT '地区配置ID',
    region_code             VARCHAR(10)                               NOT NULL UNIQUE COMMENT '地区代码',
    region_name             VARCHAR(100)                              NOT NULL COMMENT '地区名称',
    region_name_en          VARCHAR(100)                              NOT NULL COMMENT '英文名称',

    -- 地理信息
    continent               VARCHAR(20)                               NOT NULL COMMENT '大洲',
    country_codes           JSON                                      NOT NULL COMMENT '包含的国家代码列表',
    timezone_list           JSON COMMENT '时区列表',

    -- 法律和合规
    data_protection_laws    JSON COMMENT '数据保护法律',
    data_residency_required TINYINT                                            DEFAULT 0 COMMENT '是否要求数据驻留',
    cross_border_restrictions JSON COMMENT '跨境数据传输限制',

    -- 业务配置
    market_priority         ENUM ('PRIMARY', 'SECONDARY', 'EMERGING', 'RESTRICTED') DEFAULT 'SECONDARY',
    launch_phase            ENUM ('LAUNCHED', 'BETA', 'PLANNED', 'RESTRICTED')      DEFAULT 'PLANNED',

    region_status           ENUM ('ACTIVE', 'INACTIVE', 'RESTRICTED') NOT NULL      DEFAULT 'ACTIVE',

    -- 审计字段(继承BaseEntity)
    create_by            VARCHAR(64) COMMENT '创建人',
    update_by            VARCHAR(64) COMMENT '更新人',
    created_at           DATETIME                                               NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at           DATETIME                                               NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- 标淮字段
    version              int                                                             default 1 not null comment '版本号',
    deleted              tinyint                                                         default 0 not null comment '删除标记：0-未删除，1-已删除',

    PRIMARY KEY (id),
    UNIQUE KEY uk_region_code (region_code),
    KEY idx_continent (continent),
    KEY idx_market_priority (market_priority),
    KEY idx_launch_phase (launch_phase),
    KEY idx_data_residency (data_residency_required),
    KEY idx_deleted (deleted)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
    COMMENT ='全球地区配置表，支持全球化合规和本地化';

-- 数据保护策略表 - 合规管理
CREATE TABLE dtf_data_protection_policy
(
    id                    BIGINT                                                                                          NOT NULL AUTO_INCREMENT COMMENT '策略ID',
    policy_code           VARCHAR(50)                                                                                     NOT NULL UNIQUE COMMENT '策略代码',
    policy_name           VARCHAR(100)                                                                                    NOT NULL COMMENT '策略名称',
    policy_type           ENUM ('DATA_RETENTION', 'DATA_ENCRYPTION', 'ACCESS_CONTROL', 'AUDIT_LOGGING', 'CROSS_BORDER') NOT NULL COMMENT '策略类型',
    applicable_regions    JSON COMMENT '适用地区',
    applicable_data_types JSON COMMENT '适用数据类型',

    -- 策略内容
    policy_rules          JSON                                                                                            NOT NULL COMMENT '策略规则',
    implementation_guide  TEXT COMMENT '实施指南',

    -- 合规要求
    legal_basis           VARCHAR(200) COMMENT '法律依据',
    compliance_standards  JSON COMMENT '合规标准',

    -- 访问控制
    access_restrictions   JSON COMMENT '访问限制',
    encryption_required   TINYINT                                                                                                  DEFAULT 1 COMMENT '是否要求加密',
    audit_logging         TINYINT                                                                                                  DEFAULT 1 COMMENT '是否记录审计日志',

    policy_status         ENUM ('ACTIVE', 'INACTIVE', 'DRAFT')                                                    NOT NULL DEFAULT 'DRAFT',
    effective_date        DATE                                                                                    NOT NULL COMMENT '生效日期',
    expiry_date           DATE COMMENT '失效日期',

    -- 审计字段(继承BaseEntity)
    create_by            VARCHAR(64) COMMENT '创建人',
    update_by            VARCHAR(64) COMMENT '更新人',
    created_at           DATETIME                                               NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at           DATETIME                                               NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- 标淮字段
    version              int                                                             default 1 not null comment '版本号',
    deleted              tinyint                                                         default 0 not null comment '删除标记：0-未删除，1-已删除',

    PRIMARY KEY (id),
    UNIQUE KEY uk_policy_code (policy_code),
    KEY idx_policy_type (policy_type),
    KEY idx_policy_status (policy_status),
    KEY idx_effective_date (effective_date),
    KEY idx_deleted (deleted)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
    COMMENT ='数据保护策略表，支持合规管理';

SET FOREIGN_KEY_CHECKS = 1;

-- =====================================================
-- 索引优化建议
-- =====================================================

-- 为JSON字段添加生成列索引（可选，高频查询时使用）
-- 示例：为tenant表的feature_config添加功能查询优化
-- ALTER TABLE dtf_tenant
--     ADD COLUMN gallery_enabled TINYINT
--         AS (JSON_EXTRACT(feature_config, '$.gallery_enabled')) STORED,
--     ADD INDEX idx_gallery_enabled (gallery_enabled);

-- 示例：为vendor_agent表的territory_scope添加地区查询优化
-- ALTER TABLE dtf_vendor_agent
--     ADD COLUMN territory_region VARCHAR(50)
--         AS (JSON_UNQUOTE(JSON_EXTRACT(territory_scope, '$.region'))) STORED,
--     ADD INDEX idx_territory_region (territory_region);

-- =====================================================
-- 表结构验证查询
-- =====================================================

-- 验证所有表是否创建成功
SELECT
    TABLE_NAME as '表名',
    TABLE_COMMENT as '表注释',
    TABLE_ROWS as '预估行数'
FROM information_schema.TABLES
WHERE TABLE_SCHEMA = DATABASE()
  AND TABLE_NAME LIKE 'dtf_%'
ORDER BY TABLE_NAME;

-- 验证索引创建情况
SELECT
    TABLE_NAME as '表名',
    INDEX_NAME as '索引名',
    COLUMN_NAME as '列名',
    INDEX_TYPE as '索引类型'
FROM information_schema.STATISTICS
WHERE TABLE_SCHEMA = DATABASE()
  AND TABLE_NAME LIKE 'dtf_%'
ORDER BY TABLE_NAME, INDEX_NAME;
