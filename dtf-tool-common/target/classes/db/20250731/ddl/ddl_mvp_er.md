# DTF多租户用户体系核心架构表设计 v3.0 - 美国合规MVP版

## PlantUML ER关系图 (兼容PlantUML 1.2025.4+)

```plantuml
@startuml DTF_User_System_Complete_ER_Diagram

title DTF多租户用户体系核心架构表设计 v3.0 - 美国合规MVP版

package "GlobalIdentityLayer" {
  entity dtf_global_identity {
    * id : BIGINT
    * global_user_id : VARCHAR(64)
    --
    email_hash : VARCHAR(64)
    phone_hash : VARCHAR(64)
    identity_fingerprint : VARCHAR(128)
    identity_type : ENUM
    verification_level : TINYINT
    risk_score : DECIMAL(3,2)
    global_status : ENUM
    suspension_reason : VARCHAR(200)
    gdpr_consent_version : VARCHAR(20)
    data_retention_until : DATE
    last_privacy_policy_accepted_at : DATETIME
    --
    create_by : VARCHAR(64)
    update_by : VARCHAR(64)
    created_at : DATETIME
    updated_at : DATETIME
    version : INT
    deleted : TINYINT
  }
}

package "DeploymentInstanceLayer" {
  entity dtf_deployment_instance {
    * id : BIGINT
    * instance_id : VARCHAR(64)
    --
    instance_name : VARCHAR(100)
    deployment_type : ENUM
    primary_domain : VARCHAR(100)
    api_gateway_url : VARCHAR(200)
    cdn_domain : VARCHAR(100)
    database_config : JSON
    storage_config : JSON
    cache_config : JSON
    region_code : VARCHAR(10)
    data_residency_rules : JSON
    compliance_certifications : JSON
    instance_status : ENUM
    --
    create_by : VARCHAR(64)
    update_by : VARCHAR(64)
    created_at : DATETIME
    updated_at : DATETIME
    version : INT
    deleted : TINYINT
  }
}

package "TenantManagementLayer" {
  entity dtf_tenant {
    * id : BIGINT
    * tenant_code : VARCHAR(50)
    * deployment_instance_id : BIGINT
    parent_tenant_id : BIGINT
    --
    tenant_name : VARCHAR(100)
    tenant_name_en : VARCHAR(100)
    tenant_type : ENUM
    tenant_hierarchy : VARCHAR(200)
    hierarchy_level : INT
    isolation_level : ENUM
    brand_config : JSON
    theme_config : JSON
    logo_url : VARCHAR(500)
    custom_domain : VARCHAR(100)
    feature_config : JSON
    module_permissions : JSON
    api_rate_limits : JSON
    user_quota : INT
    storage_quota_gb : INT
    api_quota_daily : INT
    billing_model : ENUM
    billing_config : JSON
    subscription_plan : VARCHAR(50)
    data_processing_purposes : JSON
    retention_policies : JSON
    privacy_settings : JSON
    tenant_status : ENUM
    --
    create_by : VARCHAR(64)
    update_by : VARCHAR(64)
    created_at : DATETIME
    updated_at : DATETIME
    version : INT
    deleted : TINYINT
  }
}

package "TenantUserAccountLayer" {
  entity dtf_tenant_user_account {
    * id : BIGINT
    * tenant_id : BIGINT
    * global_user_id : VARCHAR(64)
    --
    local_user_id : VARCHAR(64)
    username : VARCHAR(50)
    email : VARCHAR(100)
    password_hash : VARCHAR(255)
    password_salt : VARCHAR(32)
    password_updated_at : DATETIME
    failed_login_attempts : TINYINT
    locked_until : DATETIME
    mfa_enabled : TINYINT
    mfa_secret : VARCHAR(32)
    backup_codes : JSON
    user_type : ENUM
    user_level : ENUM
    nickname : VARCHAR(50)
    real_name : VARCHAR(50)
    avatar_url : VARCHAR(500)
    gender : ENUM
    birth_date : DATE
    phone : VARCHAR(20)
    phone_verified : TINYINT
    address : JSON
    account_status : ENUM
    suspension_reason : VARCHAR(200)
    activation_token : VARCHAR(64)
    activation_expires_at : DATETIME
    subscription_type : VARCHAR(20)
    subscription_expires_at : DATETIME
    last_login_at : DATETIME
    last_login_ip : VARCHAR(45)
    login_count : INT
    country_code : VARCHAR(10)
    region_code : VARCHAR(10)
    language : VARCHAR(10)
    timezone : VARCHAR(50)
    gdpr_consent : TINYINT
    marketing_consent : TINYINT
    data_processing_consent : JSON
    email_verified : TINYINT
    --
    create_by : VARCHAR(64)
    update_by : VARCHAR(64)
    created_at : DATETIME
    updated_at : DATETIME
    version : INT
    deleted : TINYINT
  }
}

package "CrossInstanceMappingLayer" {
  entity dtf_cross_instance_user_mapping {
    * id : BIGINT
    * global_user_id : VARCHAR(64)
    --
    source_instance_id : VARCHAR(64)
    target_instance_id : VARCHAR(64)
    source_tenant_id : BIGINT
    target_tenant_id : BIGINT
    mapping_type : ENUM
    mapping_status : ENUM
    sync_enabled : TINYINT
    sync_config : JSON
    last_sync_time : DATETIME
    cross_border_approved : TINYINT
    compliance_requirements : JSON
    established_at : DATETIME
    expires_at : DATETIME
    --
    create_by : VARCHAR(64)
    update_by : VARCHAR(64)
    created_at : DATETIME
    updated_at : DATETIME
    version : INT
    deleted : TINYINT
  }
}

package "PermissionManagementLayer" {
  entity dtf_tenant_role {
    * id : BIGINT
    * tenant_id : BIGINT
    * role_code : VARCHAR(50)
    --
    role_name : VARCHAR(100)
    role_description : TEXT
    permissions : JSON
    data_scope : JSON
    role_level : INT
    is_system_role : TINYINT
    role_status : ENUM
    --
    create_by : VARCHAR(64)
    update_by : VARCHAR(64)
    created_at : DATETIME
    updated_at : DATETIME
    version : INT
    deleted : TINYINT
  }

  entity dtf_tenant_user_role {
    * id : BIGINT
    * tenant_id : BIGINT
    * user_id : BIGINT
    * role_id : BIGINT
    --
    granted_by : BIGINT
    granted_at : DATETIME
    expires_at : DATETIME
    role_status : ENUM
    grant_reason : VARCHAR(200)
    revoke_reason : VARCHAR(200)
    --
    create_by : VARCHAR(64)
    update_by : VARCHAR(64)
    created_at : DATETIME
    updated_at : DATETIME
    version : INT
    deleted : TINYINT
  }
}

package "BusinessSupportLayer" {
  entity dtf_official_account {
    * id : BIGINT
    * account_code : VARCHAR(50)
    * email : VARCHAR(100)
    --
    username : VARCHAR(50)
    password_hash : VARCHAR(255)
    mfa_enabled : TINYINT
    mfa_secret : VARCHAR(32)
    account_type : ENUM
    department : VARCHAR(50)
    job_title : VARCHAR(50)
    management_permissions : JSON
    gallery_permissions : JSON
    mall_permissions : JSON
    community_permissions : JSON
    audit_permissions : JSON
    ip_whitelist : JSON
    access_time_limits : JSON
    security_level : ENUM
    account_status : ENUM
    last_login_at : DATETIME
    last_login_ip : VARCHAR(45)
    login_count : INT
    --
    create_by : VARCHAR(64)
    update_by : VARCHAR(64)
    created_at : DATETIME
    updated_at : DATETIME
    version : INT
    deleted : TINYINT
  }

  entity dtf_vendor_agent {
    * id : BIGINT
    * agent_code : VARCHAR(50)
    * vendor_tenant_id : BIGINT
    --
    agent_name : VARCHAR(100)
    agent_type : ENUM
    business_license : VARCHAR(100)
    contact_person : VARCHAR(50)
    contact_email : VARCHAR(100)
    contact_phone : VARCHAR(20)
    territory_scope : JSON
    product_scope : JSON
    customer_scope : JSON
    agent_permissions : JSON
    data_access_permissions : JSON
    custom_logo_url : VARCHAR(500)
    custom_theme : JSON
    custom_domain : VARCHAR(100)
    commission_rate : DECIMAL(5,4)
    credit_limit : DECIMAL(15,2)
    agent_status : ENUM
    contract_start_date : DATE
    contract_end_date : DATE
    --
    create_by : VARCHAR(64)
    update_by : VARCHAR(64)
    created_at : DATETIME
    updated_at : DATETIME
    version : INT
    deleted : TINYINT
  }
}

package "ComplianceManagementLayer" {
  entity dtf_data_processing_consent {
    * id : BIGINT
    * user_id : BIGINT
    * tenant_id : BIGINT
    --
    processing_purpose : VARCHAR(100)
    consent_status : ENUM
    consent_method : ENUM
    consent_version : VARCHAR(20)
    consent_timestamp : DATETIME
    withdrawal_timestamp : DATETIME
    expiry_timestamp : DATETIME
    ip_address : VARCHAR(45)
    user_agent : TEXT
    consent_evidence : JSON
    --
    create_by : VARCHAR(64)
    update_by : VARCHAR(64)
    created_at : DATETIME
    updated_at : DATETIME
    version : INT
    deleted : TINYINT
  }

  entity dtf_audit_log {
    * id : BIGINT
    * tenant_id : BIGINT
    * user_id : BIGINT
    --
    operation_type : VARCHAR(50)
    resource_type : VARCHAR(50)
    resource_id : VARCHAR(100)
    operation_details : JSON
    ip_address : VARCHAR(45)
    user_agent : TEXT
    request_id : VARCHAR(64)
    session_id : VARCHAR(64)
    operation_result : ENUM
    error_message : TEXT
    operation_timestamp : DATETIME
    --
    create_by : VARCHAR(64)
    update_by : VARCHAR(64)
    created_at : DATETIME
    updated_at : DATETIME
    version : INT
    deleted : TINYINT
  }

  entity dtf_approval_workflow {
    * id : BIGINT
    * workflow_code : VARCHAR(50)
    * tenant_id : BIGINT
    --
    workflow_name : VARCHAR(100)
    workflow_type : ENUM
    workflow_steps : JSON
    approval_rules : JSON
    auto_approval_conditions : JSON
    approver_config : JSON
    approval_timeout_hours : INT
    notification_config : JSON
    workflow_status : ENUM
    workflow_version : VARCHAR(10)
    --
    create_by : VARCHAR(64)
    update_by : VARCHAR(64)
    created_at : DATETIME
    updated_at : DATETIME
    version : INT
    deleted : TINYINT
  }

  entity dtf_global_region_config {
    * id : BIGINT
    * region_code : VARCHAR(10)
    --
    region_name : VARCHAR(100)
    region_name_en : VARCHAR(100)
    continent : VARCHAR(20)
    country_codes : JSON
    timezone_list : JSON
    data_protection_laws : JSON
    data_residency_required : TINYINT
    cross_border_restrictions : JSON
    market_priority : ENUM
    launch_phase : ENUM
    region_status : ENUM
    --
    create_by : VARCHAR(64)
    update_by : VARCHAR(64)
    created_at : DATETIME
    updated_at : DATETIME
    version : INT
    deleted : TINYINT
  }

  entity dtf_data_protection_policy {
    * id : BIGINT
    * policy_code : VARCHAR(50)
    --
    policy_name : VARCHAR(100)
    policy_type : ENUM
    applicable_regions : JSON
    applicable_data_types : JSON
    policy_rules : JSON
    implementation_guide : TEXT
    legal_basis : VARCHAR(200)
    compliance_standards : JSON
    access_restrictions : JSON
    encryption_required : TINYINT
    audit_logging : TINYINT
    policy_status : ENUM
    effective_date : DATE
    expiry_date : DATE
    --
    create_by : VARCHAR(64)
    update_by : VARCHAR(64)
    created_at : DATETIME
    updated_at : DATETIME
    version : INT
    deleted : TINYINT
  }
}

dtf_global_identity ||--o{ dtf_tenant_user_account : global_user_id
dtf_deployment_instance ||--o{ dtf_tenant : deployment_instance_id
dtf_tenant ||--o{ dtf_tenant : parent_tenant_id
dtf_tenant ||--o{ dtf_tenant_user_account : tenant_id
dtf_global_identity ||--o{ dtf_cross_instance_user_mapping : global_user_id
dtf_tenant ||--o{ dtf_tenant_role : tenant_id
dtf_tenant_role ||--o{ dtf_tenant_user_role : role_id
dtf_tenant_user_account ||--o{ dtf_tenant_user_role : user_id
dtf_tenant ||--o{ dtf_vendor_agent : vendor_tenant_id
dtf_tenant_user_account ||--o{ dtf_data_processing_consent : user_id
dtf_tenant ||--o{ dtf_data_processing_consent : tenant_id
dtf_tenant_user_account ||--o{ dtf_audit_log : user_id
dtf_tenant ||--o{ dtf_audit_log : tenant_id
dtf_tenant ||--o{ dtf_approval_workflow : tenant_id

note right of dtf_global_identity
Global Identity Management Layer
- Cross-tenant user identification
- Privacy protection design (CCPA)
- Identity fingerprint anti-fraud
- Global status management
- GDPR compliance support
end note

note right of dtf_deployment_instance
Deployment Instance Management Layer
- AWS deployment support
- Network configuration management
- Infrastructure configuration
- Geographic compliance
- Multi-region deployment
end note

note right of dtf_tenant
Tenant Management Layer
- Multi-level tenant system
- Agent system support
- Brand customization configuration
- Feature permission control
- Resource quota management
- Billing model configuration
- Compliance policy configuration
end note

note right of dtf_tenant_user_account
Tenant User Account Layer
- Same email multi-tenant registration
- Password security policy
- Multi-factor authentication (MFA)
- Personal information management
- Geographic localization
- CCPA compliance fields
end note

note right of dtf_cross_instance_user_mapping
Cross-Instance Association Layer
- Independent deployment association
- Data synchronization configuration
- Cross-border transmission control
- Mapping status management
end note

note bottom of dtf_tenant_role
Permission Management System
- RBAC permission model
- Tenant-level permission isolation
- Role hierarchy management
- Permission audit tracking
end note

note bottom of dtf_data_processing_consent
Compliance Management System
- CCPA data processing consent
- SOX audit logs
- Workflow approval
- Global region configuration
- Data protection policy
end note

@enduml
```

## 架构设计说明

### 五层架构体系

1. **第一层：全局身份管理层 (GlobalIdentityLayer)**
   - 跨租户用户识别和隐私保护
   - 符合CCPA要求的数据处理设计
   - 身份指纹防欺诈机制

2. **第二层：部署实例管理层 (DeploymentInstanceLayer)**
   - 支持AWS多区域部署
   - 基础设施配置管理
   - 地理位置合规控制

3. **第三层：租户管理层 (TenantManagementLayer)**
   - 多层级租户体系
   - 代理商体系支持
   - 品牌定制和功能权限控制

4. **第四层：租户用户账户层 (TenantUserAccountLayer)**
   - 解决同邮箱多租户注册问题
   - 密码安全策略和MFA支持
   - 个人信息管理和地理本地化

5. **第五层：跨实例关联层 (CrossInstanceMappingLayer)**
   - 独立部署间用户关联
   - 数据同步配置
   - 跨境传输控制

### 业务支撑体系

- **权限管理层**: RBAC权限模型，租户级权限隔离
- **业务支撑层**: 官方账号和代理商管理
- **合规管理层**: CCPA/SOX/GDPR全面合规支持

### 兼容性优化

基于PlantUML 1.2025.4兼容性修复经验：
- ✅ 移除所有`!theme`和`!define`宏定义
- ✅ 使用英文包名和标识符
- ✅ 简化为基础PlantUML语法
- ✅ 保持架构语义完整性
- ✅ 优化注释为英文，避免中文解析问题

### 核心特性

- **多租户隔离**: 支持DATABASE/SCHEMA/TABLE三级隔离
- **同邮箱多租户**: 解决用户在不同租户下使用同一邮箱注册
- **全球化合规**: 美国CCPA、SOX法规全面支持
- **代理商体系**: 多层级代理管理和权限控制
- **跨实例映射**: 支持独立部署间的数据同步和关联