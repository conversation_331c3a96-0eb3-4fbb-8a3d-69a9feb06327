-- =====================================================
-- DTF多租户用户体系核心架构表设计 v2.0
-- =====================================================

-- 第一层：全局身份管理层
-- 解决跨租户用户识别和隐私保护

CREATE TABLE dtf_global_identity
(
    id                   BIGINT                                                 NOT NULL AUTO_INCREMENT COMMENT '全局身份ID',
    global_user_id       VARCHAR(64)                                            NOT NULL UNIQUE COMMENT '全局唯一用户标识(UUID)',

    -- 隐私保护设计
    email_hash           VARCHAR(64)                                            NOT NULL COMMENT '邮箱SHA256哈希，用于关联检测',
    phone_hash           VARCHAR(64) COMMENT '手机号哈希',
    identity_fingerprint VARCHAR(128) COMMENT '身份指纹(设备+行为特征)',

    -- 身份属性
    identity_type        ENUM ('CONSUMER', 'VENDOR_ADMIN', 'AGENT', 'OFFICIAL') NOT NULL DEFAULT 'CONSUMER',
    verification_level   TINYINT                                                NOT NULL DEFAULT 0 COMMENT '0-未验证,1-邮箱验证,2-手机验证,3-实名验证',
    risk_score           DECIMAL(3, 2)                                                   DEFAULT 0.00 COMMENT '风险评分0-1',

    -- 全局状态
    global_status        ENUM ('ACTIVE', 'SUSPENDED', 'DELETED')                NOT NULL DEFAULT 'ACTIVE',
    suspension_reason    VARCHAR(200) COMMENT '暂停原因',

    -- 合规字段
    gdpr_consent_version VARCHAR(20) COMMENT 'GDPR同意版本',
    data_retention_until DATE COMMENT '数据保留截止日期',

    -- 审计字段(继承BaseEntity)
    create_by            VARCHAR(64) COMMENT '创建人',
    update_by            VARCHAR(64) COMMENT '更新人',
    created_at           DATETIME                                               NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at           DATETIME                                               NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- 标淮字段
    version              int                                                             default 1 not null comment '版本号',
    deleted              tinyint                                                         default 0 not null comment '删除标记：0-未删除，1-已删除',

    PRIMARY KEY (id),
    UNIQUE KEY uk_global_user_id (global_user_id),
    KEY idx_email_hash (email_hash),
    KEY idx_identity_type_status (identity_type, global_status),
    KEY idx_verification_level (verification_level),
    KEY idx_created_at (created_at),
    KEY idx_deleted (deleted)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
    COMMENT ='全局用户身份表，支持跨租户用户识别和隐私保护';

-- 第二层：部署实例管理层
-- 支持SaaS、私有化、混合部署模式

CREATE TABLE dtf_deployment_instance
(
    id                        BIGINT                                                   NOT NULL AUTO_INCREMENT COMMENT '部署实例ID',
    instance_id               VARCHAR(64)                                              NOT NULL UNIQUE COMMENT '实例唯一标识',
    instance_name             VARCHAR(100)                                             NOT NULL COMMENT '实例名称',

    -- 部署配置
    deployment_type           ENUM ('SAAS', 'PRIVATE', 'HYBRID', 'EDGE')               NOT NULL COMMENT '部署类型',
    deployment_tier           ENUM ('ENTERPRISE', 'PROFESSIONAL', 'STANDARD', 'BASIC') NOT NULL COMMENT '部署等级',

    -- 网络配置
    primary_domain            VARCHAR(100)                                             NOT NULL COMMENT '主域名',
    api_gateway_url           VARCHAR(200) COMMENT 'API网关地址',
    cdn_domain                VARCHAR(100) COMMENT 'CDN域名',

    -- 基础设施配置
    database_config           JSON COMMENT '数据库配置信息',
    storage_config            JSON COMMENT '存储配置信息',
    cache_config              JSON COMMENT '缓存配置信息',
    mq_config                 JSON COMMENT '消息队列配置',

    -- 地理位置和合规
    region_code               VARCHAR(10)                                              NOT NULL COMMENT '部署区域：US/EU/CN/AP/CA',
    availability_zones        JSON COMMENT '可用区配置',
    data_residency_rules      JSON COMMENT '数据驻留规则',
    compliance_certifications JSON COMMENT '合规认证列表',

    -- 容量和性能
    max_tenants               INT                                                               DEFAULT 1000 COMMENT '最大租户数',
    max_users_per_tenant      INT                                                               DEFAULT 10000 COMMENT '每租户最大用户数',
    storage_quota_gb          INT                                                               DEFAULT 1000 COMMENT '存储配额(GB)',
    bandwidth_quota_mbps      INT                                                               DEFAULT 1000 COMMENT '带宽配额(Mbps)',

    -- 实例状态
    instance_status           ENUM ('RUNNING', 'MAINTENANCE', 'UPGRADING', 'STOPPED')  NOT NULL DEFAULT 'RUNNING',
    health_score              DECIMAL(3, 2)                                                     DEFAULT 1.00 COMMENT '健康评分0-1',
    last_health_check         DATETIME COMMENT '最后健康检查时间',

    -- 版本信息
    system_version            VARCHAR(20) COMMENT '系统版本',
    database_version          VARCHAR(20) COMMENT '数据库版本',
    last_upgrade_time         DATETIME COMMENT '最后升级时间',

    create_by                 VARCHAR(64) COMMENT '创建人',
    update_by                 VARCHAR(64) COMMENT '更新人',
    created_at                DATETIME                                                 NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at                DATETIME                                                 NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- 标淮字段
    version                   int                                                               default 1 not null comment '版本号',
    deleted                   tinyint                                                           default 0 not null comment '删除标记：0-未删除，1-已删除',

    PRIMARY KEY (id),
    UNIQUE KEY uk_instance_id (instance_id),
    KEY idx_deployment_type (deployment_type),
    KEY idx_region_code (region_code),
    KEY idx_instance_status (instance_status),
    KEY idx_deployment_tier (deployment_tier),
    KEY idx_deleted (deleted)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
    COMMENT ='部署实例表，支持多种部署模式和地理分布';

-- 第三层：租户管理层
-- 支持多层级租户和灵活的隔离策略

CREATE TABLE dtf_tenant
(
    id                       BIGINT                                                                 NOT NULL AUTO_INCREMENT COMMENT '租户ID',
    tenant_code              VARCHAR(50)                                                            NOT NULL UNIQUE COMMENT '租户代码',
    tenant_name              VARCHAR(100)                                                           NOT NULL COMMENT '租户名称',
    tenant_name_en           VARCHAR(100) COMMENT '英文名称',

    -- 租户层级
    tenant_type              ENUM ('OFFICIAL', 'DEVICE_VENDOR', 'AGENT_L1', 'AGENT_L2', 'AGENT_L3') NOT NULL COMMENT '租户类型',
    parent_tenant_id         BIGINT COMMENT '父租户ID',
    tenant_hierarchy         VARCHAR(500) COMMENT '租户层级路径',
    hierarchy_level          TINYINT                                                                         DEFAULT 0 COMMENT '层级深度',

    -- 部署配置
    deployment_instance_id   BIGINT                                                                 NOT NULL COMMENT '所属部署实例',
    isolation_level          ENUM ('DATABASE', 'SCHEMA', 'TABLE', 'ROW')                            NOT NULL DEFAULT 'TABLE' COMMENT '隔离级别',

    -- 品牌和定制
    brand_config             JSON COMMENT '品牌定制配置',
    theme_config             JSON COMMENT '主题配置',
    logo_url                 VARCHAR(500) COMMENT 'Logo URL',
    custom_domain            VARCHAR(100) COMMENT '自定义域名',

    -- 功能配置
    feature_config           JSON COMMENT '功能开关配置',
    module_permissions       JSON COMMENT '模块权限配置',
    api_rate_limits          JSON COMMENT 'API限流配置',

    -- 资源配额
    user_quota               INT                                                                             DEFAULT 100 COMMENT '用户配额',
    storage_quota_gb         INT                                                                             DEFAULT 10 COMMENT '存储配额(GB)',
    api_quota_daily          INT                                                                             DEFAULT 10000 COMMENT '每日API调用配额',
    bandwidth_quota_mbps     INT                                                                             DEFAULT 100 COMMENT '带宽配额(Mbps)',

    -- 计费配置
    billing_model            ENUM ('FREE', 'SUBSCRIPTION', 'PAY_AS_GO', 'ENTERPRISE')                        DEFAULT 'FREE',
    billing_config           JSON COMMENT '计费配置',
    subscription_plan        VARCHAR(50) COMMENT '订阅计划',
    billing_cycle            ENUM ('MONTHLY', 'QUARTERLY', 'YEARLY') COMMENT '计费周期',

    -- 合规配置
    data_processing_purposes JSON COMMENT '数据处理目的',
    retention_policies       JSON COMMENT '数据保留策略',
    privacy_settings         JSON COMMENT '隐私设置',
    audit_requirements       JSON COMMENT '审计要求',

    -- 地理和本地化
    primary_region           VARCHAR(10) COMMENT '主要区域',
    allowed_regions          JSON COMMENT '允许的区域列表',
    default_language         VARCHAR(10)                                                                     DEFAULT 'en' COMMENT '默认语言',
    supported_languages      JSON COMMENT '支持的语言列表',
    default_timezone         VARCHAR(50) COMMENT '默认时区',

    -- 租户状态
    tenant_status            ENUM ('ACTIVE', 'SUSPENDED', 'EXPIRED', 'DELETED')                     NOT NULL DEFAULT 'ACTIVE',
    suspension_reason        VARCHAR(200) COMMENT '暂停原因',
    expiry_date              DATE COMMENT '到期日期',

    -- 联系信息
    contact_email            VARCHAR(100) COMMENT '联系邮箱',
    contact_phone            VARCHAR(20) COMMENT '联系电话',
    contact_person           VARCHAR(50) COMMENT '联系人',
    business_license         VARCHAR(100) COMMENT '营业执照号',

    create_by                VARCHAR(64) COMMENT '创建人',
    update_by                VARCHAR(64) COMMENT '更新人',
    created_at               DATETIME                                                               NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at               DATETIME                                                               NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- 标淮字段
    version                  int                                                                             default 1 not null comment '版本号',
    deleted                  tinyint                                                                         default 0 not null comment '删除标记：0-未删除，1-已删除',

    PRIMARY KEY (id),
    UNIQUE KEY uk_tenant_code (tenant_code),
    KEY idx_tenant_type (tenant_type),
    KEY idx_deployment_instance (deployment_instance_id),
    KEY idx_parent_tenant (parent_tenant_id),
    KEY idx_tenant_status (tenant_status),
    KEY idx_hierarchy_level (hierarchy_level),
    -- FOREIGN KEY (deployment_instance_id) REFERENCES dtf_deployment_instance (id) ON DELETE RESTRICT,
    -- FOREIGN KEY (parent_tenant_id) REFERENCES dtf_tenant (id) ON DELETE SET NULL,
    KEY idx_deleted (deleted)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
    COMMENT ='租户表，支持多层级管理和灵活隔离策略';

-- 第四层：租户用户账户层
-- 解决同邮箱多租户注册核心问题

CREATE TABLE dtf_tenant_user_account
(
    id                      BIGINT                                                             NOT NULL AUTO_INCREMENT COMMENT '租户用户账户ID',
    tenant_id               BIGINT                                                             NOT NULL COMMENT '租户ID',
    global_user_id          VARCHAR(64)                                                        NOT NULL COMMENT '全局用户标识',

    -- 租户内账户信息
    local_user_id           VARCHAR(64)                                                        NOT NULL COMMENT '租户内用户ID',
    username                VARCHAR(50) COMMENT '用户名',
    email                   VARCHAR(100)                                                       NOT NULL COMMENT '登录邮箱',
    email_verified          TINYINT                                                            NOT NULL DEFAULT 0 COMMENT '邮箱验证状态',

    -- 密码和安全
    password_hash           VARCHAR(255)                                                       NOT NULL COMMENT '密码哈希',
    password_salt           VARCHAR(32) COMMENT '密码盐值',
    password_updated_at     DATETIME COMMENT '密码更新时间',
    failed_login_attempts   TINYINT                                                                     DEFAULT 0 COMMENT '登录失败次数',
    locked_until            DATETIME COMMENT '锁定截止时间',

    -- 多因素认证
    mfa_enabled             TINYINT                                                                     DEFAULT 0 COMMENT '是否启用MFA',
    mfa_secret              VARCHAR(32) COMMENT 'MFA密钥',
    backup_codes            JSON COMMENT '备用验证码',

    -- 用户属性
    user_type               ENUM ('CONSUMER', 'VENDOR_ADMIN', 'AGENT', 'SUPPORT', 'DEVELOPER') NOT NULL DEFAULT 'CONSUMER',
    user_level              ENUM ('BASIC', 'PREMIUM', 'VIP', 'ENTERPRISE')                              DEFAULT 'BASIC',

    -- 个人信息
    nickname                VARCHAR(50) COMMENT '昵称',
    real_name               VARCHAR(50) COMMENT '真实姓名',
    avatar_url              VARCHAR(500) COMMENT '头像URL',
    gender                  ENUM ('MALE', 'FEMALE', 'OTHER', 'UNKNOWN')                                 DEFAULT 'UNKNOWN',
    birth_date              DATE COMMENT '出生日期',

    -- 联系方式
    phone                   VARCHAR(20) COMMENT '手机号',
    phone_verified          TINYINT                                                            NOT NULL DEFAULT 0 COMMENT '手机验证状态',
    address                 JSON COMMENT '地址信息',

    -- 账户状态
    account_status          ENUM ('PENDING', 'ACTIVE', 'SUSPENDED', 'DELETED')                 NOT NULL DEFAULT 'PENDING',
    suspension_reason       VARCHAR(200) COMMENT '暂停原因',
    activation_token        VARCHAR(64) COMMENT '激活令牌',
    activation_expires_at   DATETIME COMMENT '激活令牌过期时间',

    -- 业务属性
    subscription_type       VARCHAR(20)                                                                 DEFAULT 'FREE' COMMENT '订阅类型',
    subscription_expires_at DATETIME COMMENT '订阅过期时间',

    -- 资源使用
    storage_quota           BIGINT                                                                      DEFAULT ********** COMMENT '存储配额(字节)',
    used_storage            BIGINT                                                                      DEFAULT 0 COMMENT '已用存储',
    api_quota_daily         INT                                                                         DEFAULT 1000 COMMENT '每日API配额',
    api_calls_today         INT                                                                         DEFAULT 0 COMMENT '今日API调用次数',

    -- 地理和本地化
    country_code            VARCHAR(10) COMMENT '国家代码',
    region_code             VARCHAR(10) COMMENT '地区代码',
    language                VARCHAR(10)                                                                 DEFAULT 'en' COMMENT '语言偏好',
    timezone                VARCHAR(50) COMMENT '时区',

    -- 合规字段
    gdpr_consent            TINYINT                                                                     DEFAULT 0 COMMENT 'GDPR同意状态',
    marketing_consent       TINYINT                                                                     DEFAULT 0 COMMENT '营销同意状态',
    data_processing_consent JSON COMMENT '数据处理同意记录',

    -- 登录信息
    last_login_time         DATETIME COMMENT '最后登录时间',
    last_login_ip           VARCHAR(45) COMMENT '最后登录IP',
    last_login_device       VARCHAR(200) COMMENT '最后登录设备',
    login_count             INT                                                                         DEFAULT 0 COMMENT '登录次数',

    -- 行为分析
    user_tags               JSON COMMENT '用户标签',
    preferences             JSON COMMENT '用户偏好设置',
    behavior_score          DECIMAL(3, 2)                                                               DEFAULT 0.50 COMMENT '行为评分0-1',

    create_by               VARCHAR(64) COMMENT '创建人',
    update_by               VARCHAR(64) COMMENT '更新人',
    created_at              DATETIME                                                           NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at              DATETIME                                                           NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- 标淮字段
    version                 int                                                                         default 1 not null comment '版本号',
    deleted                 tinyint                                                                     default 0 not null comment '删除标记：0-未删除，1-已删除',

    PRIMARY KEY (id),
    UNIQUE KEY uk_tenant_email (tenant_id, email) COMMENT '租户内邮箱唯一',
    UNIQUE KEY uk_tenant_local_user (tenant_id, local_user_id) COMMENT '租户内用户ID唯一',
    UNIQUE KEY uk_tenant_username (tenant_id, username) COMMENT '租户内用户名唯一',
    KEY idx_global_user_id (global_user_id),
    KEY idx_tenant_user_type (tenant_id, user_type),
    KEY idx_account_status (account_status),
    KEY idx_email_verified (email_verified),
    KEY idx_last_login_time (last_login_time),
    KEY idx_deleted (deleted)
    -- FOREIGN KEY (tenant_id) REFERENCES dtf_tenant (id) ON DELETE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
    COMMENT ='租户用户账户表，解决同邮箱多租户注册问题';

-- 第五层：跨实例关联层
-- 支持独立部署间的用户关联和数据同步

CREATE TABLE dtf_cross_instance_user_mapping
(
    id                      BIGINT                                             NOT NULL AUTO_INCREMENT COMMENT '映射ID',
    global_user_id          VARCHAR(64)                                        NOT NULL COMMENT '全局用户标识',

    -- 实例映射
    source_instance_id      VARCHAR(64)                                        NOT NULL COMMENT '源实例ID',
    target_instance_id      VARCHAR(64)                                        NOT NULL COMMENT '目标实例ID',
    source_tenant_id        BIGINT                                             NOT NULL COMMENT '源租户ID',
    target_tenant_id        BIGINT                                             NOT NULL COMMENT '目标租户ID',

    -- 映射类型
    mapping_type            ENUM ('MIGRATION', 'SYNC', 'FEDERATION', 'BACKUP') NOT NULL COMMENT '映射类型',
    mapping_direction       ENUM ('UNIDIRECTIONAL', 'BIDIRECTIONAL')                    DEFAULT 'UNIDIRECTIONAL',
    mapping_status          ENUM ('ACTIVE', 'SUSPENDED', 'REVOKED', 'EXPIRED') NOT NULL DEFAULT 'ACTIVE',

    -- 同步配置
    sync_enabled            TINYINT                                                     DEFAULT 0 COMMENT '是否启用数据同步',
    sync_scope              JSON COMMENT '同步范围配置',
    sync_frequency          ENUM ('REALTIME', 'HOURLY', 'DAILY', 'WEEKLY', 'MANUAL')    DEFAULT 'DAILY',
    last_sync_time          DATETIME COMMENT '最后同步时间',
    next_sync_time          DATETIME COMMENT '下次同步时间',

    -- 权限映射
    permission_mapping      JSON COMMENT '权限映射规则',
    data_access_scope       JSON COMMENT '数据访问范围',

    -- 合规控制
    cross_border_approved   TINYINT                                                     DEFAULT 0 COMMENT '跨境传输批准',
    compliance_requirements JSON COMMENT '合规要求',
    audit_trail             JSON COMMENT '审计轨迹',

    -- 映射状态
    established_at          DATETIME                                           NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '建立时间',
    expires_at              DATETIME COMMENT '过期时间',
    revoked_at              DATETIME COMMENT '撤销时间',
    revoked_reason          VARCHAR(200) COMMENT '撤销原因',

    create_by               VARCHAR(64) COMMENT '创建人',
    update_by               VARCHAR(64) COMMENT '更新人',
    created_at              DATETIME                                           NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at              DATETIME                                           NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- 标淮字段
    version                 int                                                         default 1 not null comment '版本号',
    deleted                 tinyint                                                     default 0 not null comment '删除标记：0-未删除，1-已删除',

    PRIMARY KEY (id),
    UNIQUE KEY uk_user_instance_mapping (global_user_id, source_instance_id, target_instance_id),
    KEY idx_source_instance (source_instance_id),
    KEY idx_target_instance (target_instance_id),
    KEY idx_mapping_type_status (mapping_type, mapping_status),
    KEY idx_sync_enabled (sync_enabled),
    KEY idx_next_sync_time (next_sync_time),
    KEY idx_deleted (deleted)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
    COMMENT ='跨实例用户映射表，支持独立部署间用户关联';


-- =====================================================
-- DTF权限管理和审计合规表设计
-- =====================================================

-- 权限角色表 - 支持RBAC和ABAC混合模式
CREATE TABLE dtf_tenant_role
(
    id                     BIGINT                                                  NOT NULL AUTO_INCREMENT COMMENT '角色ID',
    tenant_id              BIGINT                                                  NOT NULL COMMENT '租户ID',
    role_code              VARCHAR(50)                                             NOT NULL COMMENT '角色代码',
    role_name              VARCHAR(100)                                            NOT NULL COMMENT '角色名称',
    role_name_en           VARCHAR(100) COMMENT '英文名称',

    -- 角色类型
    role_type              ENUM ('SYSTEM', 'CUSTOM', 'INHERITED', 'TEMPORARY')     NOT NULL DEFAULT 'CUSTOM',
    role_category          ENUM ('ADMIN', 'USER', 'AGENT', 'SUPPORT', 'DEVELOPER') NOT NULL,
    role_level             TINYINT                                                          DEFAULT 1 COMMENT '角色级别1-10',

    -- 权限配置 - RBAC
    permissions            JSON                                                    NOT NULL COMMENT '权限列表',
    resource_permissions   JSON COMMENT '资源权限配置',
    api_permissions        JSON COMMENT 'API权限配置',

    -- 数据访问控制 - ABAC
    data_scope             JSON COMMENT '数据访问范围',
    attribute_rules        JSON COMMENT '属性规则',
    context_conditions     JSON COMMENT '上下文条件',

    -- 代理商特殊权限
    agent_permissions      JSON COMMENT '代理商权限配置',
    territory_scope        JSON COMMENT '区域管理范围',
    subordinate_scope      JSON COMMENT '下级管理范围',
    commission_permissions JSON COMMENT '佣金相关权限',

    -- 时间和条件限制
    effective_time         DATETIME COMMENT '生效时间',
    expire_time            DATETIME COMMENT '过期时间',
    usage_conditions       JSON COMMENT '使用条件',

    -- 角色状态
    role_status            ENUM ('ACTIVE', 'INACTIVE', 'DEPRECATED')               NOT NULL DEFAULT 'ACTIVE',
    deprecation_reason     VARCHAR(200) COMMENT '废弃原因',
    replacement_role_id    BIGINT COMMENT '替代角色ID',

    create_by              VARCHAR(64) COMMENT '创建人',
    update_by              VARCHAR(64) COMMENT '更新人',
    created_at             DATETIME                                                NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at             DATETIME                                                NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- 标淮字段
    version                int                                                              default 1 not null comment '版本号',
    deleted                tinyint                                                          default 0 not null comment '删除标记：0-未删除，1-已删除',

    PRIMARY KEY (id),
    UNIQUE KEY uk_tenant_role_code (tenant_id, role_code),
    KEY idx_role_type_category (role_type, role_category),
    KEY idx_role_status (role_status),
    KEY idx_role_level (role_level),
    KEY idx_deleted (deleted)
    -- FOREIGN KEY (tenant_id) REFERENCES dtf_tenant (id) ON DELETE CASCADE,
    -- FOREIGN KEY (replacement_role_id) REFERENCES dtf_tenant_role (id) ON DELETE SET NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
    COMMENT ='租户角色表，支持RBAC和ABAC混合权限模式';

-- 用户角色关联表 - 支持动态权限分配
CREATE TABLE dtf_tenant_user_role
(
    id                   BIGINT                                                   NOT NULL AUTO_INCREMENT COMMENT '用户角色关联ID',
    tenant_id            BIGINT                                                   NOT NULL COMMENT '租户ID',
    user_account_id      BIGINT                                                   NOT NULL COMMENT '用户账户ID',
    role_id              BIGINT                                                   NOT NULL COMMENT '角色ID',

    -- 分配信息
    assignment_type      ENUM ('DIRECT', 'INHERITED', 'TEMPORARY', 'CONDITIONAL') NOT NULL DEFAULT 'DIRECT',
    granted_by           BIGINT COMMENT '授权人ID',
    granted_reason       VARCHAR(200) COMMENT '授权原因',

    -- 权限生效配置
    effective_time       DATETIME                                                 NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '生效时间',
    expire_time          DATETIME COMMENT '过期时间',
    auto_extend          TINYINT                                                           DEFAULT 0 COMMENT '是否自动延期',

    -- 条件和限制
    usage_conditions     JSON COMMENT '使用条件',
    ip_restrictions      JSON COMMENT 'IP限制',
    time_restrictions    JSON COMMENT '时间限制',
    device_restrictions  JSON COMMENT '设备限制',

    -- 权限覆盖
    permission_overrides JSON COMMENT '权限覆盖配置',
    data_scope_overrides JSON COMMENT '数据范围覆盖',

    -- 分配状态
    assignment_status    ENUM ('ACTIVE', 'SUSPENDED', 'EXPIRED', 'REVOKED')       NOT NULL DEFAULT 'ACTIVE',
    suspension_reason    VARCHAR(200) COMMENT '暂停原因',
    revoked_by           BIGINT COMMENT '撤销人ID',
    revoked_reason       VARCHAR(200) COMMENT '撤销原因',

    create_by            VARCHAR(64) COMMENT '创建人',
    update_by            VARCHAR(64) COMMENT '更新人',
    created_at           DATETIME                                                 NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at           DATETIME                                                 NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- 标淮字段
    version              int                                                               default 1 not null comment '版本号',
    deleted              tinyint                                                           default 0 not null comment '删除标记：0-未删除，1-已删除',

    PRIMARY KEY (id),
    UNIQUE KEY uk_user_role_assignment (user_account_id, role_id, assignment_type),
    KEY idx_tenant_user (tenant_id, user_account_id),
    KEY idx_role_id (role_id),
    KEY idx_assignment_status (assignment_status),
    KEY idx_effective_expire (effective_time, expire_time),
    KEY idx_deleted (deleted)
    -- FOREIGN KEY (tenant_id) REFERENCES dtf_tenant (id) ON DELETE CASCADE,
    -- FOREIGN KEY (user_account_id) REFERENCES dtf_tenant_user_account (id) ON DELETE CASCADE,
    -- FOREIGN KEY (role_id) REFERENCES dtf_tenant_role (id) ON DELETE CASCADE,
    -- FOREIGN KEY (granted_by) REFERENCES dtf_tenant_user_account (id) ON DELETE SET NULL,
    -- FOREIGN KEY (revoked_by) REFERENCES dtf_tenant_user_account (id) ON DELETE SET NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
    COMMENT ='用户角色关联表，支持动态权限分配和条件控制';

-- 数据处理目的表 - GDPR合规核心
CREATE TABLE dtf_data_processing_purpose
(
    id                        BIGINT                                                                                                     NOT NULL AUTO_INCREMENT COMMENT '处理目的ID',
    tenant_id                 BIGINT                                                                                                     NOT NULL COMMENT '租户ID',
    purpose_code              VARCHAR(50)                                                                                                NOT NULL COMMENT '目的代码',
    purpose_name              VARCHAR(100)                                                                                               NOT NULL COMMENT '目的名称',
    purpose_description       TEXT COMMENT '目的描述',

    -- 法律依据
    legal_basis               ENUM ('CONSENT', 'CONTRACT', 'LEGAL_OBLIGATION', 'VITAL_INTERESTS', 'PUBLIC_TASK', 'LEGITIMATE_INTERESTS') NOT NULL,
    legal_basis_description   TEXT COMMENT '法律依据描述',

    -- 数据类别和处理
    data_categories           JSON                                                                                                       NOT NULL COMMENT '涉及的数据类别',
    processing_activities     JSON COMMENT '处理活动描述',
    automated_decision_making TINYINT                                                                                                             DEFAULT 0 COMMENT '是否涉及自动化决策',
    profiling                 TINYINT                                                                                                             DEFAULT 0 COMMENT '是否涉及用户画像',

    -- 数据接收方
    data_recipients           JSON COMMENT '数据接收方',
    third_party_sharing       TINYINT                                                                                                             DEFAULT 0 COMMENT '是否与第三方共享',
    cross_border_transfer     TINYINT                                                                                                             DEFAULT 0 COMMENT '是否跨境传输',
    transfer_safeguards       JSON COMMENT '跨境传输保障措施',

    -- 数据保留
    retention_days            INT                                                                                                        NOT NULL COMMENT '数据保留天数',
    retention_criteria        TEXT COMMENT '保留标准',
    auto_deletion             TINYINT                                                                                                             DEFAULT 1 COMMENT '是否自动删除',
    deletion_method           ENUM ('SOFT_DELETE', 'HARD_DELETE', 'ANONYMIZATION')                                                                DEFAULT 'SOFT_DELETE',

    -- 数据主体权利
    access_right              TINYINT                                                                                                             DEFAULT 1 COMMENT '访问权',
    rectification_right       TINYINT                                                                                                             DEFAULT 1 COMMENT '更正权',
    erasure_right             TINYINT                                                                                                             DEFAULT 1 COMMENT '删除权',
    portability_right         TINYINT                                                                                                             DEFAULT 1 COMMENT '可携带权',
    restriction_right         TINYINT                                                                                                             DEFAULT 1 COMMENT '限制处理权',
    objection_right           TINYINT                                                                                                             DEFAULT 1 COMMENT '反对权',

    -- 风险评估
    risk_level                ENUM ('LOW', 'MEDIUM', 'HIGH', 'VERY_HIGH')                                                                         DEFAULT 'MEDIUM',
    impact_assessment         JSON COMMENT '影响评估结果',
    mitigation_measures       JSON COMMENT '风险缓解措施',

    purpose_status            ENUM ('ACTIVE', 'SUSPENDED', 'TERMINATED')                                                                 NOT NULL DEFAULT 'ACTIVE',

    create_by                 VARCHAR(64) COMMENT '创建人',
    update_by                 VARCHAR(64) COMMENT '更新人',
    created_at                DATETIME                                                                                                   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at                DATETIME                                                                                                   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- 标淮字段
    version                   int                                                                                                                 default 1 not null comment '版本号',
    deleted                   tinyint                                                                                                             default 0 not null comment '删除标记：0-未删除，1-已删除',

    PRIMARY KEY (id),
    UNIQUE KEY uk_tenant_purpose_code (tenant_id, purpose_code),
    KEY idx_legal_basis (legal_basis),
    KEY idx_risk_level (risk_level),
    KEY idx_purpose_status (purpose_status),
    KEY idx_deleted (deleted)
    -- FOREIGN KEY (tenant_id) REFERENCES dtf_tenant (id) ON DELETE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
    COMMENT ='数据处理目的表，GDPR合规核心支持';

-- 用户同意记录表 - 同意管理
CREATE TABLE dtf_user_consent
(
    id                   BIGINT                                                            NOT NULL AUTO_INCREMENT COMMENT '同意记录ID',
    tenant_id            BIGINT                                                            NOT NULL COMMENT '租户ID',
    user_account_id      BIGINT                                                            NOT NULL COMMENT '用户账户ID',
    purpose_id           BIGINT                                                            NOT NULL COMMENT '处理目的ID',

    -- 同意状态
    consent_status       ENUM ('GIVEN', 'WITHDRAWN', 'EXPIRED', 'INVALID')                 NOT NULL,
    consent_method       ENUM ('WEB_FORM', 'MOBILE_APP', 'EMAIL', 'PHONE', 'PAPER', 'API') NOT NULL,
    consent_version      VARCHAR(20) COMMENT '同意版本',

    -- 同意时间
    consent_timestamp    DATETIME                                                          NOT NULL COMMENT '同意时间',
    withdrawal_timestamp DATETIME COMMENT '撤回时间',
    expiry_timestamp     DATETIME COMMENT '过期时间',

    -- 技术记录
    ip_address           VARCHAR(45) COMMENT 'IP地址',
    user_agent           TEXT COMMENT '用户代理',
    device_fingerprint   VARCHAR(128) COMMENT '设备指纹',
    session_id           VARCHAR(64) COMMENT '会话ID',

    -- 同意证据
    consent_evidence     JSON COMMENT '同意证据',
    consent_text         TEXT COMMENT '同意文本',
    consent_language     VARCHAR(10) COMMENT '同意语言',

    -- 处理记录
    processed_by         VARCHAR(64) COMMENT '处理人',
    processing_notes     TEXT COMMENT '处理说明',

    create_by            VARCHAR(64) COMMENT '创建人',
    created_at           DATETIME                                                          NOT NULL DEFAULT CURRENT_TIMESTAMP,

    -- 标淮字段
    version              int     default 1                                                 not null comment '版本号',
    deleted              tinyint default 0                                                 not null comment '删除标记：0-未删除，1-已删除',

    PRIMARY KEY (id),
    UNIQUE KEY uk_user_purpose_consent (user_account_id, purpose_id, consent_timestamp),
    KEY idx_tenant_user (tenant_id, user_account_id),
    KEY idx_consent_status (consent_status),
    KEY idx_consent_timestamp (consent_timestamp),
    KEY idx_withdrawal_timestamp (withdrawal_timestamp),
    KEY idx_deleted (deleted)
    -- FOREIGN KEY (tenant_id) REFERENCES dtf_tenant (id) ON DELETE CASCADE,
    -- FOREIGN KEY (user_account_id) REFERENCES dtf_tenant_user_account (id) ON DELETE CASCADE,
    -- FOREIGN KEY (purpose_id) REFERENCES dtf_data_processing_purpose (id) ON DELETE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
    COMMENT ='用户同意记录表，支持GDPR同意管理';

-- 系统操作审计表 - 完整审计追踪
CREATE TABLE dtf_system_audit_log
(
    id                  BIGINT                                                                             NOT NULL AUTO_INCREMENT COMMENT '审计日志ID',
    tenant_id           BIGINT COMMENT '租户ID',
    instance_id         VARCHAR(64)                                                                        NOT NULL COMMENT '实例ID',

    -- 操作主体
    operator_type       ENUM ('USER', 'SYSTEM', 'API', 'BATCH', 'EXTERNAL')                                NOT NULL,
    operator_id         VARCHAR(64) COMMENT '操作者ID',
    operator_name       VARCHAR(100) COMMENT '操作者名称',
    operator_ip         VARCHAR(45) COMMENT '操作IP',
    operator_device     VARCHAR(200) COMMENT '操作设备',

    -- 操作详情
    operation_type      VARCHAR(50)                                                                        NOT NULL COMMENT '操作类型',
    operation_category  ENUM ('CREATE', 'READ', 'UPDATE', 'DELETE', 'LOGIN', 'LOGOUT', 'EXPORT', 'IMPORT') NOT NULL,
    resource_type       VARCHAR(50)                                                                        NOT NULL COMMENT '资源类型',
    resource_id         VARCHAR(64) COMMENT '资源ID',
    resource_name       VARCHAR(200) COMMENT '资源名称',

    -- 操作内容
    operation_details   JSON COMMENT '操作详情',
    request_data        JSON COMMENT '请求数据',
    response_data       JSON COMMENT '响应数据',
    affected_records    INT     DEFAULT 0 COMMENT '影响记录数',

    -- 操作结果
    operation_result    ENUM ('SUCCESS', 'FAILED', 'PARTIAL', 'CANCELLED')                                 NOT NULL,
    error_code          VARCHAR(20) COMMENT '错误代码',
    error_message       TEXT COMMENT '错误信息',
    execution_time_ms   INT COMMENT '执行时间(毫秒)',

    -- 合规字段
    data_classification ENUM ('PUBLIC', 'INTERNAL', 'CONFIDENTIAL', 'RESTRICTED') COMMENT '数据分类',
    sensitivity_level   ENUM ('LOW', 'MEDIUM', 'HIGH', 'CRITICAL') COMMENT '敏感级别',
    compliance_tags     JSON COMMENT '合规标签',
    retention_until     DATE COMMENT '保留截止日期',

    -- 上下文信息
    session_id          VARCHAR(64) COMMENT '会话ID',
    transaction_id      VARCHAR(64) COMMENT '事务ID',
    correlation_id      VARCHAR(64) COMMENT '关联ID',
    parent_operation_id BIGINT COMMENT '父操作ID',

    -- 地理信息
    country_code        VARCHAR(10) COMMENT '国家代码',
    region_code         VARCHAR(10) COMMENT '地区代码',

    created_at          DATETIME                                                                           NOT NULL DEFAULT CURRENT_TIMESTAMP,

    -- 标淮字段
    version             int     default 1                                                                  not null comment '版本号',
    deleted             tinyint default 0                                                                  not null comment '删除标记：0-未删除，1-已删除',

    PRIMARY KEY (id),
    KEY idx_tenant_operator (tenant_id, operator_id),
    KEY idx_operation_type_category (operation_type, operation_category),
    KEY idx_resource_type_id (resource_type, resource_id),
    KEY idx_operation_result (operation_result),
    KEY idx_created_at (created_at),
    KEY idx_data_classification (data_classification),
    KEY idx_correlation_id (correlation_id),
    KEY idx_deleted (deleted)
    -- FOREIGN KEY (tenant_id) REFERENCES dtf_tenant (id) ON DELETE SET NULL,
    -- FOREIGN KEY (parent_operation_id) REFERENCES dtf_system_audit_log (id) ON DELETE SET NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
    COMMENT ='系统操作审计表，支持完整合规审计追踪';

-- =====================================================
-- DTF业务模块支撑表设计 - 官方账号、设备厂、C端用户具体需求
-- =====================================================

-- 官方账号管理表 - 独立的官方账号体系
CREATE TABLE dtf_official_account
(
    id                     BIGINT                                                            NOT NULL AUTO_INCREMENT COMMENT '官方账号ID',
    account_code           VARCHAR(50)                                                       NOT NULL UNIQUE COMMENT '账号代码',
    email                  VARCHAR(100)                                                      NOT NULL UNIQUE COMMENT '官方邮箱',
    username               VARCHAR(50)                                                       NOT NULL COMMENT '用户名',

    -- 密码和安全
    password_hash          VARCHAR(255)                                                      NOT NULL COMMENT '密码哈希',
    mfa_enabled            TINYINT                                                                    DEFAULT 1 COMMENT '强制MFA',
    mfa_secret             VARCHAR(32) COMMENT 'MFA密钥',

    -- 官方账号属性
    account_type           ENUM ('SUPER_ADMIN', 'ADMIN', 'OPERATOR', 'AUDITOR', 'DEVELOPER') NOT NULL,
    department             VARCHAR(50) COMMENT '所属部门',
    job_title              VARCHAR(50) COMMENT '职位',

    -- 权限范围
    management_permissions JSON COMMENT '管理后台权限',
    gallery_permissions    JSON COMMENT '图库管理权限',
    mall_permissions       JSON COMMENT '商城管理权限',
    community_permissions  JSON COMMENT '社区管理权限',
    audit_permissions      JSON COMMENT '审核权限',

    -- 数据访问范围
    data_access_scope      JSON COMMENT '数据访问范围',
    tenant_access_scope    JSON COMMENT '租户访问范围',
    region_access_scope    JSON COMMENT '地区访问范围',

    -- 账号状态
    account_status         ENUM ('ACTIVE', 'SUSPENDED', 'LOCKED', 'DELETED')                 NOT NULL DEFAULT 'ACTIVE',
    last_login_time        DATETIME COMMENT '最后登录时间',
    login_count            INT                                                                        DEFAULT 0 COMMENT '登录次数',

    create_by              VARCHAR(64) COMMENT '创建人',
    update_by              VARCHAR(64) COMMENT '更新人',
    created_at             DATETIME                                                          NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at             DATETIME                                                          NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- 标淮字段
    version                int                                                                        default 1 not null comment '版本号',
    deleted                tinyint                                                                    default 0 not null comment '删除标记：0-未删除，1-已删除',

    PRIMARY KEY (id),
    UNIQUE KEY uk_account_code (account_code),
    UNIQUE KEY uk_email (email),
    KEY idx_account_type (account_type),
    KEY idx_department (department),
    KEY idx_account_status (account_status),
    KEY idx_deleted (deleted)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
    COMMENT ='官方账号表，独立的超级管理员体系';

-- 设备厂代理管理表 - 多级代理体系
CREATE TABLE dtf_vendor_agent
(
    id                        BIGINT                                                    NOT NULL AUTO_INCREMENT COMMENT '代理ID',
    tenant_id                 BIGINT                                                    NOT NULL COMMENT '设备厂租户ID',
    agent_code                VARCHAR(50)                                               NOT NULL COMMENT '代理代码',
    agent_name                VARCHAR(100)                                              NOT NULL COMMENT '代理名称',

    -- 代理层级
    parent_agent_id           BIGINT COMMENT '上级代理ID',
    agent_level               TINYINT                                                   NOT NULL COMMENT '代理级别1-5',
    agent_hierarchy           VARCHAR(500) COMMENT '代理层级路径',

    -- 代理属性
    agent_type                ENUM ('DIRECT', 'INDIRECT', 'EXCLUSIVE', 'NON_EXCLUSIVE') NOT NULL,
    business_license          VARCHAR(100) COMMENT '营业执照',
    contact_person            VARCHAR(50) COMMENT '联系人',
    contact_email             VARCHAR(100) COMMENT '联系邮箱',
    contact_phone             VARCHAR(20) COMMENT '联系电话',

    -- 管理范围
    territory_scope           JSON COMMENT '管理区域范围',
    product_scope             JSON COMMENT '产品范围',
    customer_scope            JSON COMMENT '客户范围',

    -- 权限配置
    agent_permissions         JSON COMMENT '代理权限配置',
    data_access_permissions   JSON COMMENT '数据访问权限',
    customization_permissions JSON COMMENT '定制化权限',

    -- 定制化设置
    custom_logo_url           VARCHAR(500) COMMENT '自定义Logo',
    custom_theme              JSON COMMENT '自定义主题',
    custom_domain             VARCHAR(100) COMMENT '自定义域名',

    -- 业务配置
    commission_rate           DECIMAL(5, 4)                                                      DEFAULT 0.0000 COMMENT '佣金比例',
    sales_target              DECIMAL(15, 2) COMMENT '销售目标',
    credit_limit              DECIMAL(15, 2) COMMENT '信用额度',

    -- 代理状态
    agent_status              ENUM ('ACTIVE', 'SUSPENDED', 'TERMINATED', 'PENDING')     NOT NULL DEFAULT 'PENDING',
    contract_start_date       DATE COMMENT '合同开始日期',
    contract_end_date         DATE COMMENT '合同结束日期',

    create_by                 VARCHAR(64) COMMENT '创建人',
    update_by                 VARCHAR(64) COMMENT '更新人',
    created_at                DATETIME                                                  NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at                DATETIME                                                  NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- 标淮字段
    version                   int                                                                default 1 not null comment '版本号',
    deleted                   tinyint                                                            default 0 not null comment '删除标记：0-未删除，1-已删除',

    PRIMARY KEY (id),
    UNIQUE KEY uk_tenant_agent_code (tenant_id, agent_code),
    KEY idx_parent_agent (parent_agent_id),
    KEY idx_agent_level (agent_level),
    KEY idx_agent_status (agent_status),
    KEY idx_deleted (deleted)
    -- 移除JSON字段索引：KEY idx_territory_scope (territory_scope(100)),
    -- FOREIGN KEY (tenant_id) REFERENCES dtf_tenant (id) ON DELETE CASCADE,
    -- FOREIGN KEY (parent_agent_id) REFERENCES dtf_vendor_agent (id) ON DELETE SET NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
    COMMENT ='设备厂代理管理表，支持多级代理体系';

-- 可选：JSON字段查询优化（生成列索引）
-- 仅在需要高频查询JSON字段时使用

-- 示例：为territory_scope添加地区查询优化
ALTER TABLE dtf_vendor_agent
    ADD COLUMN territory_region VARCHAR(50)
                                AS (JSON_UNQUOTE(JSON_EXTRACT(territory_scope, '$.region'))) STORED,
    ADD INDEX idx_territory_region (territory_region);

-- 示例：为agent_permissions添加权限类型查询优化
ALTER TABLE dtf_vendor_agent
    ADD COLUMN permission_level VARCHAR(20)
                                AS (JSON_UNQUOTE(JSON_EXTRACT(agent_permissions, '$.level'))) STORED,
    ADD INDEX idx_permission_level (permission_level);


-- C端用户设备管理表 - 多端同步支持
CREATE TABLE dtf_user_device
(
    id                  BIGINT                                                                                          NOT NULL AUTO_INCREMENT COMMENT '用户设备ID',
    tenant_id           BIGINT                                                                                          NOT NULL COMMENT '租户ID',
    user_account_id     BIGINT                                                                                          NOT NULL COMMENT '用户账户ID',
    device_id           VARCHAR(64)                                                                                     NOT NULL COMMENT '设备唯一标识',

    -- 设备信息
    device_type         ENUM ('PC_WINDOWS', 'PC_MAC', 'MOBILE_IOS', 'MOBILE_ANDROID', 'WEB_BROWSER', 'HARDWARE_DEVICE') NOT NULL,
    device_name         VARCHAR(100) COMMENT '设备名称',
    device_model        VARCHAR(100) COMMENT '设备型号',
    device_brand        VARCHAR(50) COMMENT '设备品牌',

    -- 系统信息
    os_type             VARCHAR(50) COMMENT '操作系统类型',
    os_version          VARCHAR(50) COMMENT '操作系统版本',
    app_version         VARCHAR(20) COMMENT '应用版本',
    hardware_info       JSON COMMENT '硬件信息',

    -- 网络信息
    ip_address          VARCHAR(45) COMMENT 'IP地址',
    mac_address         VARCHAR(17) COMMENT 'MAC地址',
    device_fingerprint  VARCHAR(128) COMMENT '设备指纹',

    -- 使用统计
    first_login_time    DATETIME COMMENT '首次登录时间',
    last_login_time     DATETIME COMMENT '最后登录时间',
    total_usage_hours   DECIMAL(10, 2)                                                                                           DEFAULT 0.00 COMMENT '总使用时长(小时)',
    login_count         INT                                                                                                      DEFAULT 0 COMMENT '登录次数',

    -- 同步配置
    sync_enabled        TINYINT                                                                                                  DEFAULT 1 COMMENT '是否启用同步',
    sync_scope          JSON COMMENT '同步范围配置',
    last_sync_time      DATETIME COMMENT '最后同步时间',
    sync_status         ENUM ('SUCCESS', 'FAILED', 'PENDING', 'DISABLED')                                                        DEFAULT 'PENDING',

    -- 设备状态
    device_status       ENUM ('ACTIVE', 'INACTIVE', 'BLOCKED', 'DELETED')                                               NOT NULL DEFAULT 'ACTIVE',
    is_primary          TINYINT                                                                                                  DEFAULT 0 COMMENT '是否主设备',
    trust_level         TINYINT                                                                                                  DEFAULT 1 COMMENT '信任级别1-5',

    -- 故障和维护
    fault_reports       JSON COMMENT '故障报告记录',
    maintenance_records JSON COMMENT '维护记录',
    consumable_usage    JSON COMMENT '耗材使用记录',

    create_by           VARCHAR(64) COMMENT '创建人',
    update_by           VARCHAR(64) COMMENT '更新人',
    created_at          DATETIME                                                                                        NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at          DATETIME                                                                                        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- 标淮字段
    version             int                                                                                                      default 1 not null comment '版本号',
    deleted             tinyint                                                                                                  default 0 not null comment '删除标记：0-未删除，1-已删除',

    PRIMARY KEY (id),
    UNIQUE KEY uk_user_device (user_account_id, device_id),
    KEY idx_tenant_user (tenant_id, user_account_id),
    KEY idx_device_type (device_type),
    KEY idx_device_status (device_status),
    KEY idx_last_login_time (last_login_time),
    KEY idx_sync_status (sync_status),
    KEY idx_deleted (deleted)
    -- FOREIGN KEY (tenant_id) REFERENCES dtf_tenant (id) ON DELETE CASCADE,
    -- FOREIGN KEY (user_account_id) REFERENCES dtf_tenant_user_account (id) ON DELETE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
    COMMENT ='C端用户设备管理表，支持多端同步和设备监控';

-- 业务模块权限表 - 细化业务权限
CREATE TABLE dtf_business_module_permission
(
    id                     BIGINT                                                                              NOT NULL AUTO_INCREMENT COMMENT '业务权限ID',
    tenant_id              BIGINT                                                                              NOT NULL COMMENT '租户ID',
    module_code            VARCHAR(50)                                                                         NOT NULL COMMENT '模块代码',
    module_name            VARCHAR(100)                                                                        NOT NULL COMMENT '模块名称',

    -- 模块分类
    module_category        ENUM ('GALLERY', 'MALL', 'COMMUNITY', 'AI_TOOLS', 'DEVICE_MONITOR', 'DESIGN_TOOLS') NOT NULL,
    module_type            ENUM ('CORE', 'PREMIUM', 'ENTERPRISE', 'CUSTOM')                                    NOT NULL DEFAULT 'CORE',

    -- 权限定义
    permission_code        VARCHAR(50)                                                                         NOT NULL COMMENT '权限代码',
    permission_name        VARCHAR(100)                                                                        NOT NULL COMMENT '权限名称',
    permission_description TEXT COMMENT '权限描述',

    -- 权限类型
    permission_type        ENUM ('READ', 'WRITE', 'DELETE', 'APPROVE', 'CONFIGURE', 'EXPORT', 'IMPORT')        NOT NULL,
    resource_type          VARCHAR(50) COMMENT '资源类型',
    operation_scope        JSON COMMENT '操作范围',

    -- 权限级别
    permission_level       ENUM ('BASIC', 'ADVANCED', 'EXPERT', 'ADMIN')                                       NOT NULL DEFAULT 'BASIC',
    required_role_level    TINYINT                                                                                      DEFAULT 1 COMMENT '所需角色级别',

    -- 条件限制
    usage_conditions       JSON COMMENT '使用条件',
    quota_limits           JSON COMMENT '配额限制',
    time_restrictions      JSON COMMENT '时间限制',

    -- 权限状态
    permission_status      ENUM ('ACTIVE', 'DEPRECATED', 'DISABLED')                                           NOT NULL DEFAULT 'ACTIVE',
    effective_time         DATETIME COMMENT '生效时间',
    expire_time            DATETIME COMMENT '过期时间',

    create_by              VARCHAR(64) COMMENT '创建人',
    update_by              VARCHAR(64) COMMENT '更新人',
    created_at             DATETIME                                                                            NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at             DATETIME                                                                            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- 标淮字段
    version                int                                                                                          default 1 not null comment '版本号',
    deleted                tinyint                                                                                      default 0 not null comment '删除标记：0-未删除，1-已删除',

    PRIMARY KEY (id),
    UNIQUE KEY uk_tenant_module_permission (tenant_id, module_code, permission_code),
    KEY idx_module_category (module_category),
    KEY idx_permission_type (permission_type),
    KEY idx_permission_level (permission_level),
    KEY idx_permission_status (permission_status),
    KEY idx_deleted (deleted)
    -- FOREIGN KEY (tenant_id) REFERENCES dtf_tenant (id) ON DELETE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
    COMMENT ='业务模块权限表，支持细粒度业务权限控制';

-- 审核工作流表 - 统一审核流程
CREATE TABLE dtf_approval_workflow
(
    id                       BIGINT                                                                                                 NOT NULL AUTO_INCREMENT COMMENT '审核工作流ID',
    tenant_id                BIGINT                                                                                                 NOT NULL COMMENT '租户ID',
    workflow_code            VARCHAR(50)                                                                                            NOT NULL COMMENT '工作流代码',
    workflow_name            VARCHAR(100)                                                                                           NOT NULL COMMENT '工作流名称',

    -- 工作流类型
    workflow_type            ENUM ('CONTENT_AUDIT', 'FUNCTION_AUDIT', 'BUSINESS_AUDIT', 'COMPLIANCE_AUDIT')                         NOT NULL,
    business_type            ENUM ('GALLERY_UPLOAD', 'MALL_PRODUCT', 'COMMUNITY_POST', 'VENDOR_CUSTOMIZATION', 'USER_REGISTRATION') NOT NULL,

    -- 工作流配置
    workflow_steps           JSON                                                                                                   NOT NULL COMMENT '工作流步骤配置',
    approval_rules           JSON COMMENT '审核规则',
    auto_approval_conditions JSON COMMENT '自动审核条件',
    escalation_rules         JSON COMMENT '升级规则',

    -- 审核人员配置
    approver_config          JSON COMMENT '审核人员配置',
    backup_approvers         JSON COMMENT '备用审核人员',
    approval_timeout_hours   INT                                                                                                             DEFAULT 24 COMMENT '审核超时时间(小时)',

    -- 通知配置
    notification_config      JSON COMMENT '通知配置',
    reminder_intervals       JSON COMMENT '提醒间隔配置',

    -- 工作流状态
    workflow_status          ENUM ('ACTIVE', 'INACTIVE', 'DRAFT', 'DEPRECATED')                                                     NOT NULL DEFAULT 'DRAFT',
    workflow_version         VARCHAR(10)                                                                                                     DEFAULT '1.0' COMMENT '版本号',

    create_by                VARCHAR(64) COMMENT '创建人',
    update_by                VARCHAR(64) COMMENT '更新人',
    created_at               DATETIME                                                                                               NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at               DATETIME                                                                                               NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- 标淮字段
    version                  int                                                                                                             default 1 not null comment '版本号',
    deleted                  tinyint                                                                                                         default 0 not null comment '删除标记：0-未删除，1-已删除',

    PRIMARY KEY (id),
    UNIQUE KEY uk_tenant_workflow_code (tenant_id, workflow_code),
    KEY idx_workflow_type (workflow_type),
    KEY idx_business_type (business_type),
    KEY idx_workflow_status (workflow_status),
    KEY idx_deleted (deleted)
    -- FOREIGN KEY (tenant_id) REFERENCES dtf_tenant (id) ON DELETE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
    COMMENT ='审核工作流表，支持统一的审核流程管理';

-- 审核记录表 - 完整审核轨迹
CREATE TABLE dtf_approval_record
(
    id                 BIGINT                                                                          NOT NULL AUTO_INCREMENT COMMENT '审核记录ID',
    tenant_id          BIGINT                                                                          NOT NULL COMMENT '租户ID',
    workflow_id        BIGINT                                                                          NOT NULL COMMENT '工作流ID',
    business_id        VARCHAR(64)                                                                     NOT NULL COMMENT '业务对象ID',

    -- 申请信息
    applicant_type     ENUM ('USER', 'VENDOR', 'AGENT', 'SYSTEM')                                      NOT NULL,
    applicant_id       VARCHAR(64)                                                                     NOT NULL COMMENT '申请人ID',
    application_data   JSON COMMENT '申请数据',
    application_reason TEXT COMMENT '申请原因',

    -- 审核状态
    approval_status    ENUM ('PENDING', 'IN_PROGRESS', 'APPROVED', 'REJECTED', 'CANCELLED', 'EXPIRED') NOT NULL DEFAULT 'PENDING',
    current_step       TINYINT                                                                                  DEFAULT 1 COMMENT '当前步骤',
    total_steps        TINYINT                                                                         NOT NULL COMMENT '总步骤数',

    -- 审核结果
    approval_result    JSON COMMENT '审核结果详情',
    final_decision     ENUM ('APPROVED', 'REJECTED', 'CONDITIONAL_APPROVED') COMMENT '最终决定',
    rejection_reason   TEXT COMMENT '拒绝原因',
    conditions         TEXT COMMENT '条件说明',

    -- 时间信息
    submitted_at       DATETIME                                                                        NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '提交时间',
    started_at         DATETIME COMMENT '开始审核时间',
    completed_at       DATETIME COMMENT '完成时间',
    deadline           DATETIME COMMENT '截止时间',

    -- 审核人员
    current_approver   VARCHAR(64) COMMENT '当前审核人',
    approval_history   JSON COMMENT '审核历史记录',

    -- 优先级和分类
    priority           ENUM ('LOW', 'NORMAL', 'HIGH', 'URGENT')                                                 DEFAULT 'NORMAL',
    risk_level         ENUM ('LOW', 'MEDIUM', 'HIGH', 'CRITICAL')                                               DEFAULT 'MEDIUM',

    create_by          VARCHAR(64) COMMENT '创建人',
    update_by          VARCHAR(64) COMMENT '更新人',
    created_at         DATETIME                                                                        NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at         DATETIME                                                                        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- 标淮字段
    version            int                                                                                      default 1 not null comment '版本号',
    deleted            tinyint                                                                                  default 0 not null comment '删除标记：0-未删除，1-已删除',

    PRIMARY KEY (id),
    UNIQUE KEY uk_workflow_business (workflow_id, business_id),
    KEY idx_tenant_applicant (tenant_id, applicant_id),
    KEY idx_approval_status (approval_status),
    KEY idx_current_approver (current_approver),
    KEY idx_submitted_at (submitted_at),
    KEY idx_priority_risk (priority, risk_level),
    KEY idx_deleted (deleted)
    -- FOREIGN KEY (tenant_id) REFERENCES dtf_tenant (id) ON DELETE CASCADE,
    -- FOREIGN KEY (workflow_id) REFERENCES dtf_approval_workflow (id) ON DELETE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
    COMMENT ='审核记录表，支持完整的审核轨迹追踪';


-- =====================================================
-- DTF全球化合规支撑表设计 - 美国市场优先，全球扩展
-- =====================================================

-- 全球地区配置表 - 支持全球化部署
CREATE TABLE dtf_global_region_config
(
    id                      BIGINT                                    NOT NULL AUTO_INCREMENT COMMENT '地区配置ID',
    region_code             VARCHAR(10)                               NOT NULL UNIQUE COMMENT '地区代码',
    region_name             VARCHAR(100)                              NOT NULL COMMENT '地区名称',
    region_name_en          VARCHAR(100)                              NOT NULL COMMENT '英文名称',

    -- 地理信息
    continent               VARCHAR(20)                               NOT NULL COMMENT '大洲',
    country_codes           JSON                                      NOT NULL COMMENT '包含的国家代码列表',
    timezone_list           JSON COMMENT '时区列表',

    -- 法律法规
    data_protection_laws    JSON COMMENT '数据保护法律',
    privacy_regulations     JSON COMMENT '隐私法规要求',
    cross_border_rules      JSON COMMENT '跨境传输规则',
    retention_requirements  JSON COMMENT '数据保留要求',

    -- 本地化要求
    supported_languages     JSON COMMENT '支持的语言',
    currency_codes          JSON COMMENT '支持的货币',
    payment_methods         JSON COMMENT '支持的支付方式',
    local_holidays          JSON COMMENT '当地节假日',

    -- 技术要求
    data_residency_required TINYINT                                                 DEFAULT 0 COMMENT '是否要求数据本地化',
    encryption_standards    JSON COMMENT '加密标准要求',
    audit_requirements      JSON COMMENT '审计要求',
    certification_needed    JSON COMMENT '所需认证',

    -- 业务配置
    market_priority         ENUM ('PRIMARY', 'SECONDARY', 'EMERGING', 'RESTRICTED') DEFAULT 'SECONDARY',
    launch_phase            ENUM ('LAUNCHED', 'BETA', 'PLANNED', 'RESTRICTED')      DEFAULT 'PLANNED',
    feature_restrictions    JSON COMMENT '功能限制',

    region_status           ENUM ('ACTIVE', 'INACTIVE', 'RESTRICTED') NOT NULL      DEFAULT 'ACTIVE',

    create_by               VARCHAR(64) COMMENT '创建人',
    update_by               VARCHAR(64) COMMENT '更新人',
    created_at              DATETIME                                  NOT NULL      DEFAULT CURRENT_TIMESTAMP,
    updated_at              DATETIME                                  NOT NULL      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- 标淮字段
    version                 int                                                     default 1 not null comment '版本号',
    deleted                 tinyint                                                 default 0 not null comment '删除标记：0-未删除，1-已删除',

    PRIMARY KEY (id),
    UNIQUE KEY uk_region_code (region_code),
    KEY idx_continent (continent),
    KEY idx_market_priority (market_priority),
    KEY idx_launch_phase (launch_phase),
    KEY idx_data_residency (data_residency_required),
    KEY idx_deleted (deleted)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
    COMMENT ='全球地区配置表，支持全球化合规和本地化';

-- 数据本地化存储表 - 数据驻留管理
CREATE TABLE dtf_data_residency_policy
(
    id                    BIGINT                                                                                  NOT NULL AUTO_INCREMENT COMMENT '数据驻留策略ID',
    tenant_id             BIGINT                                                                                  NOT NULL COMMENT '租户ID',
    region_code           VARCHAR(10)                                                                             NOT NULL COMMENT '地区代码',

    -- 数据分类
    data_category         ENUM ('USER_PERSONAL', 'BUSINESS_DATA', 'CONTENT_DATA', 'TRANSACTION_DATA', 'LOG_DATA') NOT NULL,
    data_sensitivity      ENUM ('PUBLIC', 'INTERNAL', 'CONFIDENTIAL', 'RESTRICTED')                               NOT NULL,

    -- 存储策略
    storage_location      VARCHAR(100)                                                                            NOT NULL COMMENT '存储位置',
    backup_locations      JSON COMMENT '备份位置',
    allowed_regions       JSON COMMENT '允许的地区',
    restricted_regions    JSON COMMENT '限制的地区',

    -- 传输控制
    cross_border_allowed  TINYINT                                                                                          DEFAULT 0 COMMENT '是否允许跨境传输',
    transfer_mechanisms   JSON COMMENT '传输机制',
    transfer_safeguards   JSON COMMENT '传输保障措施',
    approval_required     TINYINT                                                                                          DEFAULT 1 COMMENT '是否需要审批',

    -- 保留策略
    retention_period_days INT                                                                                     NOT NULL COMMENT '保留期限(天)',
    deletion_method       ENUM ('SOFT_DELETE', 'HARD_DELETE', 'ANONYMIZATION', 'ENCRYPTION')                               DEFAULT 'SOFT_DELETE',
    auto_deletion_enabled TINYINT                                                                                          DEFAULT 1 COMMENT '是否自动删除',

    -- 访问控制
    access_restrictions   JSON COMMENT '访问限制',
    encryption_required   TINYINT                                                                                          DEFAULT 1 COMMENT '是否要求加密',
    audit_logging         TINYINT                                                                                          DEFAULT 1 COMMENT '是否记录审计日志',

    -- 合规监控
    compliance_checks     JSON COMMENT '合规检查项',
    violation_alerts      JSON COMMENT '违规告警配置',

    policy_status         ENUM ('ACTIVE', 'INACTIVE', 'DRAFT')                                                    NOT NULL DEFAULT 'DRAFT',
    effective_date        DATE                                                                                    NOT NULL COMMENT '生效日期',
    expiry_date           DATE COMMENT '失效日期',

    create_by             VARCHAR(64) COMMENT '创建人',
    update_by             VARCHAR(64) COMMENT '更新人',
    created_at            DATETIME                                                                                NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at            DATETIME                                                                                NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- 标淮字段
    version               int                                                                                              default 1 not null comment '版本号',
    deleted               tinyint                                                                                          default 0 not null comment '删除标记：0-未删除，1-已删除',

    PRIMARY KEY (id),
    UNIQUE KEY uk_tenant_region_category (tenant_id, region_code, data_category),
    KEY idx_data_sensitivity (data_sensitivity),
    KEY idx_cross_border_allowed (cross_border_allowed),
    KEY idx_policy_status (policy_status),
    KEY idx_effective_date (effective_date),
    KEY idx_deleted (deleted)
    -- FOREIGN KEY (tenant_id) REFERENCES dtf_tenant (id) ON DELETE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
    COMMENT ='数据驻留策略表，支持数据本地化存储管理';

-- 独立部署实例隔离表 - 加强独立部署数据隔离
CREATE TABLE dtf_independent_deployment_isolation
(
    id                        BIGINT                                                  NOT NULL AUTO_INCREMENT COMMENT '隔离配置ID',
    deployment_instance_id    BIGINT                                                  NOT NULL COMMENT '部署实例ID',
    tenant_id                 BIGINT                                                  NOT NULL COMMENT '租户ID',

    -- 隔离级别
    isolation_type            ENUM ('COMPLETE', 'PARTIAL', 'FEDERATED', 'HYBRID')     NOT NULL DEFAULT 'COMPLETE',
    data_isolation_level      ENUM ('PHYSICAL', 'LOGICAL', 'ENCRYPTED', 'ANONYMIZED') NOT NULL,

    -- 官方访问控制
    official_access_allowed   TINYINT                                                          DEFAULT 0 COMMENT '是否允许官方访问',
    access_approval_required  TINYINT                                                          DEFAULT 1 COMMENT '访问是否需要审批',
    access_audit_required     TINYINT                                                          DEFAULT 1 COMMENT '访问是否需要审计',
    emergency_access_allowed  TINYINT                                                          DEFAULT 0 COMMENT '是否允许紧急访问',

    -- 数据共享控制
    data_sharing_enabled      TINYINT                                                          DEFAULT 0 COMMENT '是否启用数据共享',
    shared_data_types         JSON COMMENT '共享的数据类型',
    sharing_conditions        JSON COMMENT '共享条件',
    sharing_approval_workflow VARCHAR(64) COMMENT '共享审批流程',

    -- 同步控制
    sync_to_central           TINYINT                                                          DEFAULT 0 COMMENT '是否同步到中央',
    sync_data_scope           JSON COMMENT '同步数据范围',
    sync_frequency            ENUM ('REALTIME', 'HOURLY', 'DAILY', 'WEEKLY', 'MANUAL')         DEFAULT 'MANUAL',
    sync_encryption_required  TINYINT                                                          DEFAULT 1 COMMENT '同步是否要求加密',

    -- 监控和审计
    monitoring_enabled        TINYINT                                                          DEFAULT 1 COMMENT '是否启用监控',
    audit_trail_required      TINYINT                                                          DEFAULT 1 COMMENT '是否要求审计轨迹',
    compliance_reporting      TINYINT                                                          DEFAULT 1 COMMENT '是否需要合规报告',

    -- 技术配置
    encryption_config         JSON COMMENT '加密配置',
    network_isolation_config  JSON COMMENT '网络隔离配置',
    access_control_config     JSON COMMENT '访问控制配置',
    backup_config             JSON COMMENT '备份配置',

    -- 合规配置
    regulatory_requirements   JSON COMMENT '法规要求',
    certification_status      JSON COMMENT '认证状态',
    compliance_audit_schedule JSON COMMENT '合规审计计划',

    isolation_status          ENUM ('ACTIVE', 'INACTIVE', 'MAINTENANCE', 'VIOLATION') NOT NULL DEFAULT 'ACTIVE',

    create_by                 VARCHAR(64) COMMENT '创建人',
    update_by                 VARCHAR(64) COMMENT '更新人',
    created_at                DATETIME                                                NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at                DATETIME                                                NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- 标淮字段
    version                   int                                                              default 1 not null comment '版本号',
    deleted                   tinyint                                                          default 0 not null comment '删除标记：0-未删除，1-已删除',

    PRIMARY KEY (id),
    UNIQUE KEY uk_instance_tenant (deployment_instance_id, tenant_id),
    KEY idx_isolation_type (isolation_type),
    KEY idx_official_access (official_access_allowed),
    KEY idx_data_sharing (data_sharing_enabled),
    KEY idx_isolation_status (isolation_status),
    KEY idx_deleted (deleted)
    -- FOREIGN KEY (deployment_instance_id) REFERENCES dtf_deployment_instance (id) ON DELETE CASCADE,
    -- FOREIGN KEY (tenant_id) REFERENCES dtf_tenant (id) ON DELETE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
    COMMENT ='独立部署隔离配置表，确保独立部署数据安全隔离';

-- 跨境数据传输审批表 - 跨境传输合规管理
CREATE TABLE dtf_cross_border_transfer_approval
(
    id                     BIGINT                                                                                     NOT NULL AUTO_INCREMENT COMMENT '跨境传输审批ID',
    tenant_id              BIGINT                                                                                     NOT NULL COMMENT '租户ID',
    transfer_request_id    VARCHAR(64)                                                                                NOT NULL UNIQUE COMMENT '传输请求ID',

    -- 传输信息
    source_region          VARCHAR(10)                                                                                NOT NULL COMMENT '源地区',
    target_region          VARCHAR(10)                                                                                NOT NULL COMMENT '目标地区',
    data_category          ENUM ('USER_PERSONAL', 'BUSINESS_DATA', 'CONTENT_DATA', 'TRANSACTION_DATA')                NOT NULL,
    data_volume_mb         DECIMAL(15, 2) COMMENT '数据量(MB)',

    -- 传输目的
    transfer_purpose       ENUM ('BUSINESS_OPERATION', 'DATA_BACKUP', 'DISASTER_RECOVERY', 'ANALYTICS', 'COMPLIANCE') NOT NULL,
    business_justification TEXT                                                                                       NOT NULL COMMENT '业务理由',
    legal_basis            VARCHAR(100) COMMENT '法律依据',

    -- 保障措施
    safeguard_measures     JSON                                                                                       NOT NULL COMMENT '保障措施',
    encryption_method      VARCHAR(50) COMMENT '加密方法',
    access_controls        JSON COMMENT '访问控制',
    retention_period_days  INT COMMENT '保留期限',

    -- 审批流程
    approval_status        ENUM ('PENDING', 'APPROVED', 'REJECTED', 'CONDITIONAL', 'EXPIRED')                         NOT NULL DEFAULT 'PENDING',
    approver_id            VARCHAR(64) COMMENT '审批人ID',
    approval_conditions    TEXT COMMENT '审批条件',
    rejection_reason       TEXT COMMENT '拒绝原因',

    -- 时间信息
    requested_at           DATETIME                                                                                   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '申请时间',
    approved_at            DATETIME COMMENT '审批时间',
    valid_until            DATETIME COMMENT '有效期至',

    -- 执行记录
    transfer_executed      TINYINT                                                                                             DEFAULT 0 COMMENT '是否已执行',
    execution_time         DATETIME COMMENT '执行时间',
    execution_result       JSON COMMENT '执行结果',

    -- 监控和审计
    monitoring_required    TINYINT                                                                                             DEFAULT 1 COMMENT '是否需要监控',
    audit_trail            JSON COMMENT '审计轨迹',
    compliance_report      JSON COMMENT '合规报告',

    create_by              VARCHAR(64) COMMENT '创建人',
    update_by              VARCHAR(64) COMMENT '更新人',
    created_at             DATETIME                                                                                   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at             DATETIME                                                                                   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- 标淮字段
    version                int                                                                                                 default 1 not null comment '版本号',
    deleted                tinyint                                                                                             default 0 not null comment '删除标记：0-未删除，1-已删除',

    PRIMARY KEY (id),
    UNIQUE KEY uk_transfer_request_id (transfer_request_id),
    KEY idx_tenant_source_target (tenant_id, source_region, target_region),
    KEY idx_approval_status (approval_status),
    KEY idx_data_category (data_category),
    KEY idx_requested_at (requested_at),
    KEY idx_valid_until (valid_until),
    KEY idx_deleted (deleted)
    -- FOREIGN KEY (tenant_id) REFERENCES dtf_tenant (id) ON DELETE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
    COMMENT ='跨境数据传输审批表，支持跨境传输合规管理';

