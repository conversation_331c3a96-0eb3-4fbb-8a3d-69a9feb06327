-- =====================================================
-- DTF多租户用户体系初始数据插入 v2.0
-- 基于DDL文件完整表结构，添加version和deleted字段
-- =====================================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =====================================================
-- 第一层：全局身份管理初始数据
-- =====================================================

-- 插入系统管理员和核心用户全局身份
INSERT INTO dtf_global_identity (id, global_user_id, email_hash, phone_hash, identity_type,
                                 verification_level, risk_score, global_status,
                                 version, deleted,
                                 create_by, created_at, updated_at)
VALUES (1, 'GLOBAL-ADMIN-001', SHA2('<EMAIL>', 256), SHA2('******-DTF-HELP', 256), 'OFFICIAL', 3, 0.00, 'ACTIVE', 1, 0, 'SYSTEM', NOW(), NOW()),
       (2, 'GLOBAL-ADMIN-002', SHA2('<EMAIL>', 256), SHA2('******-DTF-SUPER', 256), 'OFFICIAL', 3, 0.00, 'ACTIVE', 1, 0, 'SYSTEM', NOW(), NOW()),
       (3, 'GLOBAL-VENDOR-OPPO-001', SHA2('<EMAIL>', 256), SHA2('+86-400-OPPO-001', 256), 'VENDOR_ADMIN', 2, 0.00, 'ACTIVE', 1, 0, 'SYSTEM', NOW(), NOW()),
       (4, 'GLOBAL-VENDOR-XIAOMI-001', SHA2('<EMAIL>', 256), SHA2('+86-400-MI-0001', 256), 'VENDOR_ADMIN', 2, 0.00, 'ACTIVE', 1, 0, 'SYSTEM', NOW(), NOW()),
       (5, 'GLOBAL-VENDOR-SAMSUNG-001', SHA2('<EMAIL>', 256), SHA2('******-SAMSUNG', 256), 'VENDOR_ADMIN', 2, 0.00, 'ACTIVE', 1, 0, 'SYSTEM', NOW(), NOW()),
       (6, 'GLOBAL-AGENT-US-001', SHA2('<EMAIL>', 256), SHA2('+1-415-OPPO-001', 256), 'AGENT', 1, 0.00, 'ACTIVE', 1, 0, 'SYSTEM', NOW(), NOW()),
       (7, 'GLOBAL-AGENT-EU-001', SHA2('<EMAIL>', 256), SHA2('+49-800-MI-EU', 256), 'AGENT', 1, 0.00, 'ACTIVE', 1, 0, 'SYSTEM', NOW(), NOW());

-- 插入部署实例配置
INSERT INTO dtf_deployment_instance (id, instance_id, instance_name, deployment_type, deployment_tier,
                                     primary_domain, api_gateway_url, cdn_domain, region_code,
                                     max_tenants, max_users_per_tenant, instance_status, health_score,
                                     database_config, storage_config, cache_config, mq_config,
                                     version, deleted,
                                     create_by, created_at, updated_at)
VALUES (1, 'DTF-SAAS-GLOBAL-001', 'DTF全球SaaS主实例', 'SAAS', 'ENTERPRISE',
        'dtf.global.com', 'https://api.dtf.global.com', 'https://cdn.dtf.global.com', 'GLOBAL',
        10000, 100000, 'RUNNING', 1.00,
        '{"host": "db.dtf.global.com", "port": 3306, "cluster": true}',
        '{"type": "S3", "bucket": "dtf-global-storage", "region": "us-east-1"}',
        '{"type": "Redis", "cluster": "redis.dtf.global.com:6379"}',
        '{"type": "RabbitMQ", "host": "mq.dtf.global.com", "vhost": "/dtf"}',
        1, 0, 'SYSTEM', NOW(), NOW()),

       (2, 'DTF-SAAS-US-001', 'DTF美国SaaS实例', 'SAAS', 'ENTERPRISE',
        'us.dtf.com', 'https://api.us.dtf.com', 'https://cdn.us.dtf.com', 'US',
        5000, 50000, 'RUNNING', 0.98,
        '{"host": "db.us.dtf.com", "port": 3306, "cluster": true}',
        '{"type": "S3", "bucket": "dtf-us-storage", "region": "us-west-2"}',
        '{"type": "Redis", "cluster": "redis.us.dtf.com:6379"}',
        '{"type": "RabbitMQ", "host": "mq.us.dtf.com", "vhost": "/dtf-us"}',
        1, 0, 'SYSTEM', NOW(), NOW()),

       (3, 'DTF-PRIVATE-OPPO-001', 'OPPO私有化部署实例', 'PRIVATE', 'ENTERPRISE',
        'dtf.oppo.internal', 'https://api.dtf.oppo.internal', 'https://cdn.dtf.oppo.internal', 'CN',
        100, 10000, 'RUNNING', 0.95,
        '{"host": "db.oppo.internal", "port": 3306, "cluster": false}',
        '{"type": "MinIO", "endpoint": "storage.oppo.internal"}',
        '{"type": "Redis", "host": "redis.oppo.internal:6379"}',
        '{"type": "RabbitMQ", "host": "mq.oppo.internal", "vhost": "/oppo"}',
        1, 0, 'SYSTEM', NOW(), NOW()),

       (4, 'DTF-HYBRID-EU-001', 'DTF欧盟混合部署实例', 'HYBRID', 'PROFESSIONAL',
        'eu.dtf.com', 'https://api.eu.dtf.com', 'https://cdn.eu.dtf.com', 'EU',
        2000, 20000, 'RUNNING', 0.97,
        '{"host": "db.eu.dtf.com", "port": 3306, "cluster": true}',
        '{"type": "S3", "bucket": "dtf-eu-storage", "region": "eu-west-1"}',
        '{"type": "Redis", "cluster": "redis.eu.dtf.com:6379"}',
        '{"type": "RabbitMQ", "host": "mq.eu.dtf.com", "vhost": "/dtf-eu"}',
        1, 0, 'SYSTEM', NOW(), NOW());

-- =====================================================
-- 第三层：租户管理初始数据
-- =====================================================

INSERT INTO dtf_tenant (id, tenant_code, tenant_name, tenant_name_en, tenant_type,
                        parent_tenant_id, tenant_hierarchy, hierarchy_level,
                        deployment_instance_id, isolation_level,
                        brand_config, theme_config, logo_url, custom_domain,
                        feature_config, module_permissions, api_rate_limits,
                        user_quota, storage_quota_gb, api_quota_daily, bandwidth_quota_mbps,
                        billing_model, billing_config, subscription_plan, billing_cycle,
                        data_processing_purposes, retention_policies, privacy_settings, audit_requirements,
                        primary_region, allowed_regions, default_language, supported_languages, default_timezone,
                        tenant_status, contact_email, contact_phone, contact_person,
                        version, deleted,
                        create_by, created_at, updated_at)
VALUES
-- 官方系统租户
(1, 'DTF-OFFICIAL-SYSTEM', 'DTF官方系统租户', 'DTF Official System Tenant', 'OFFICIAL',
 NULL, '/1', 0, 1, 'DATABASE',
 '{"brand_name": "DTF Official", "primary_color": "#1890ff", "logo": "dtf-logo.png"}',
 '{"theme": "default", "dark_mode": false}', 'https://cdn.dtf.com/logo/official.png', 'admin.dtf.com',
 '{"all_modules": true, "advanced_features": true}',
 '{"all_permissions": true}',
 '{"requests_per_minute": 10000, "burst": 20000}',
 999999, 999999, 999999, 999999,
 'ENTERPRISE', '{"plan_type": "unlimited", "auto_renewal": true}', 'ENTERPRISE_UNLIMITED', 'YEARLY',
 '["user_account_management", "system_administration", "compliance_audit"]',
 '{"user_data": 2555, "system_logs": 1095, "audit_records": 2555}',
 '{"data_encryption": true, "access_logging": true, "gdpr_compliant": true}',
 '{"audit_level": "full", "retention_days": 2555, "compliance_standards": ["SOC2", "ISO27001"]}',
 'GLOBAL', '["GLOBAL", "US", "EU", "CN", "AP"]', 'en', '["en", "zh", "es", "fr", "de"]', 'UTC',
 'ACTIVE', '<EMAIL>', '******-DTF-HELP', 'DTF System Administrator',
 1, 0, 'SYSTEM', NOW(), NOW()),

-- 设备厂商租户
(2, 'OPPO-GLOBAL-TENANT', 'OPPO全球租户', 'OPPO Global Tenant', 'DEVICE_VENDOR',
 1, '/1/2', 1, 1, 'SCHEMA',
 '{"brand_name": "OPPO", "primary_color": "#07C160", "logo": "oppo-logo.png"}',
 '{"theme": "oppo", "dark_mode": false}', 'https://cdn.dtf.com/logo/oppo.png', 'oppo.dtf.com',
 '{"device_management": true, "user_analytics": true, "api_access": true}',
 '{"device_management": true, "user_management": true, "analytics": true}',
 '{"requests_per_minute": 5000, "burst": 10000}',
 50000, 10000, 500000, 1000,
 'ENTERPRISE', '{"plan_type": "premium", "auto_renewal": true, "custom_features": true}', 'ENTERPRISE_PREMIUM', 'YEARLY',
 '["device_monitoring", "user_analytics", "performance_optimization"]',
 '{"device_data": 1095, "user_data": 730, "analytics_data": 365}',
 '{"data_encryption": true, "regional_compliance": true}',
 '{"audit_level": "standard", "retention_days": 1095}',
 'GLOBAL', '["GLOBAL", "CN", "US", "EU", "AP"]', 'zh', '["zh", "en", "es"]', 'Asia/Shanghai',
 'ACTIVE', '<EMAIL>', '+86-400-OPPO-001', 'OPPO Global Administrator',
 1, 0, 'SYSTEM', NOW(), NOW()),

(3, 'XIAOMI-GLOBAL-TENANT', '小米全球租户', 'Xiaomi Global Tenant', 'DEVICE_VENDOR',
 1, '/1/3', 1, 1, 'SCHEMA',
 '{"brand_name": "Xiaomi", "primary_color": "#FF6900", "logo": "xiaomi-logo.png"}',
 '{"theme": "xiaomi", "dark_mode": false}', 'https://cdn.dtf.com/logo/xiaomi.png', 'xiaomi.dtf.com',
 '{"device_management": true, "user_analytics": true, "api_access": true}',
 '{"device_management": true, "user_management": true, "analytics": true}',
 '{"requests_per_minute": 5000, "burst": 10000}',
 50000, 10000, 500000, 1000,
 'ENTERPRISE', '{"plan_type": "premium", "auto_renewal": true, "custom_features": true}', 'ENTERPRISE_PREMIUM', 'YEARLY',
 '["device_monitoring", "user_analytics", "performance_optimization"]',
 '{"device_data": 1095, "user_data": 730, "analytics_data": 365}',
 '{"data_encryption": true, "regional_compliance": true}',
 '{"audit_level": "standard", "retention_days": 1095}',
 'GLOBAL', '["GLOBAL", "CN", "US", "EU", "AP"]', 'zh', '["zh", "en", "es"]', 'Asia/Shanghai',
 'ACTIVE', '<EMAIL>', '+86-400-MI-0001', 'Xiaomi Global Administrator',
 1, 0, 'SYSTEM', NOW(), NOW()),

(4, 'SAMSUNG-US-TENANT', '三星美国租户', 'Samsung US Tenant', 'DEVICE_VENDOR',
 1, '/1/4', 1, 2, 'SCHEMA',
 '{"brand_name": "Samsung", "primary_color": "#1428A0", "logo": "samsung-logo.png"}',
 '{"theme": "samsung", "dark_mode": false}', 'https://cdn.dtf.com/logo/samsung.png', 'samsung.dtf.com',
 '{"device_management": true, "user_analytics": true, "api_access": true}',
 '{"device_management": true, "user_management": true, "analytics": true}',
 '{"requests_per_minute": 3000, "burst": 6000}',
 30000, 5000, 300000, 500,
 'ENTERPRISE', '{"plan_type": "professional", "auto_renewal": true}', 'PROFESSIONAL_PREMIUM', 'YEARLY',
 '["device_monitoring", "user_analytics"]',
 '{"device_data": 730, "user_data": 365}',
 '{"data_encryption": true, "ccpa_compliant": true}',
 '{"audit_level": "standard", "retention_days": 730}',
 'US', '["US"]', 'en', '["en", "es"]', 'America/New_York',
 'ACTIVE', '<EMAIL>', '******-SAMSUNG', 'Samsung US Administrator',
 1, 0, 'SYSTEM', NOW(), NOW()),

-- 代理商租户
(5, 'OPPO-US-AGENT-L1', 'OPPO美国一级代理', 'OPPO US Level 1 Agent', 'AGENT_L1',
 2, '/1/2/5', 2, 2, 'TABLE',
 '{"brand_name": "OPPO US Agent", "primary_color": "#07C160", "logo": "oppo-us-logo.png"}',
 '{"theme": "oppo", "dark_mode": false}', 'https://cdn.dtf.com/logo/oppo-us.png', 'oppo-us.dtf.com',
 '{"device_management": true, "customer_support": true}',
 '{"device_view": true, "customer_support": true}',
 '{"requests_per_minute": 1000, "burst": 2000}',
 5000, 1000, 50000, 100,
 'SUBSCRIPTION', '{"plan_type": "professional", "auto_renewal": true}', 'PROFESSIONAL_STANDARD', 'YEARLY',
 '["customer_support", "device_basic_info"]',
 '{"customer_data": 365, "support_logs": 180}',
 '{"basic_encryption": true}',
 '{"audit_level": "basic", "retention_days": 365}',
 'US', '["US"]', 'en', '["en"]', 'America/Los_Angeles',
 'ACTIVE', '<EMAIL>', '******-OPPO-US', 'OPPO US Agent Manager',
 1, 0, 'SYSTEM', NOW(), NOW()),

(6, 'XIAOMI-EU-AGENT-L1', '小米欧盟一级代理', 'Xiaomi EU Level 1 Agent', 'AGENT_L1',
 3, '/1/3/6', 2, 4, 'TABLE',
 '{"brand_name": "Xiaomi EU Agent", "primary_color": "#FF6900", "logo": "xiaomi-eu-logo.png"}',
 '{"theme": "xiaomi", "dark_mode": false}', 'https://cdn.dtf.com/logo/xiaomi-eu.png', 'xiaomi-eu.dtf.com',
 '{"device_management": true, "customer_support": true}',
 '{"device_view": true, "customer_support": true}',
 '{"requests_per_minute": 800, "burst": 1600}',
 3000, 500, 30000, 50,
 'SUBSCRIPTION', '{"plan_type": "standard", "auto_renewal": true}', 'STANDARD_PREMIUM', 'YEARLY',
 '["customer_support", "device_basic_info"]',
 '{"customer_data": 365, "support_logs": 180}',
 '{"basic_encryption": true, "gdpr_compliant": true}',
 '{"audit_level": "basic", "retention_days": 365}',
 'EU', '["EU"]', 'de', '["de", "en"]', 'Europe/Berlin',
 'ACTIVE', '<EMAIL>', '+49-800-MI-EU', 'Xiaomi EU Agent Manager',
 1, 0, 'SYSTEM', NOW(), NOW());

-- 插入系统管理员和核心用户账户
INSERT INTO dtf_tenant_user_account (id, tenant_id, global_user_id, local_user_id, username, email, email_verified,
                                     password_hash, password_salt, password_updated_at,
                                     mfa_enabled, user_type, user_level,
                                     nickname, real_name, avatar_url, gender,
                                     phone, phone_verified, country_code, region_code, language, timezone,
                                     account_status, subscription_type, subscription_expires_at,
                                     storage_quota, api_quota_daily,
                                     version, deleted,
                                     create_by, created_at, updated_at)
VALUES
-- 官方系统管理员
(1, 1, 'GLOBAL-ADMIN-001', 'LOCAL-ADMIN-001', 'dtf_admin', '<EMAIL>', 1,
 '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5S/kS', 'salt_admin_001', NOW(),
 1, 'SUPPORT', 'ENTERPRISE',
 'DTF管理员', 'DTF Administrator', 'https://cdn.dtf.com/avatar/admin.png', 'UNKNOWN',
 '******-DTF-HELP', 1, 'US', 'GLOBAL', 'en', 'UTC',
 'ACTIVE', 'ENTERPRISE', DATE_ADD(NOW(), INTERVAL 10 YEAR),
 ************, 999999,
 1, 0, 'SYSTEM', NOW(), NOW()),

(2, 1, 'GLOBAL-ADMIN-002', 'LOCAL-ADMIN-002', 'dtf_superadmin', '<EMAIL>', 1,
 '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5S/kS', 'salt_admin_002', NOW(),
 1, 'SUPPORT', 'ENTERPRISE',
 'DTF超级管理员', 'DTF Super Administrator', 'https://cdn.dtf.com/avatar/superadmin.png', 'UNKNOWN',
 '******-DTF-SUPER', 1, 'US', 'GLOBAL', 'en', 'UTC',
 'ACTIVE', 'ENTERPRISE', DATE_ADD(NOW(), INTERVAL 10 YEAR),
 ************, 999999,
 1, 0, 'SYSTEM', NOW(), NOW()),

-- OPPO管理员账户
(3, 2, 'GLOBAL-VENDOR-OPPO-001', 'LOCAL-OPPO-ADMIN-001', 'oppo_admin', '<EMAIL>', 1,
 '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5S/kS', 'salt_oppo_001', NOW(),
 1, 'VENDOR_ADMIN', 'ENTERPRISE',
 'OPPO管理员', 'OPPO Administrator', 'https://cdn.dtf.com/avatar/oppo-admin.png', 'UNKNOWN',
 '+86-400-OPPO-001', 1, 'CN', 'GLOBAL', 'zh', 'Asia/Shanghai',
 'ACTIVE', 'ENTERPRISE', DATE_ADD(NOW(), INTERVAL 5 YEAR),
 10737418240, 500000,
 1, 0, 'SYSTEM', NOW(), NOW()),

-- 小米管理员账户
(4, 3, 'GLOBAL-VENDOR-XIAOMI-001', 'LOCAL-XIAOMI-ADMIN-001', 'xiaomi_admin', '<EMAIL>', 1,
 '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5S/kS', 'salt_xiaomi_001', NOW(),
 1, 'VENDOR_ADMIN', 'ENTERPRISE',
 '小米管理员', 'Xiaomi Administrator', 'https://cdn.dtf.com/avatar/xiaomi-admin.png', 'UNKNOWN',
 '+86-400-MI-0001', 1, 'CN', 'GLOBAL', 'zh', 'Asia/Shanghai',
 'ACTIVE', 'ENTERPRISE', DATE_ADD(NOW(), INTERVAL 5 YEAR),
 10737418240, 500000,
 1, 0, 'SYSTEM', NOW(), NOW()),

-- 三星管理员账户
(5, 4, 'GLOBAL-VENDOR-SAMSUNG-001', 'LOCAL-SAMSUNG-ADMIN-001', 'samsung_admin', '<EMAIL>', 1,
 '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5S/kS', 'salt_samsung_001', NOW(),
 1, 'VENDOR_ADMIN', 'ENTERPRISE',
 '三星管理员', 'Samsung Administrator', 'https://cdn.dtf.com/avatar/samsung-admin.png', 'UNKNOWN',
 '******-SAMSUNG', 1, 'US', 'US', 'en', 'America/New_York',
 'ACTIVE', 'ENTERPRISE', DATE_ADD(NOW(), INTERVAL 3 YEAR),
 *********0, 300000,
 1, 0, 'SYSTEM', NOW(), NOW()),

-- 代理商管理员账户 (修正user_level值)
(6, 5, 'GLOBAL-AGENT-US-001', 'LOCAL-AGENT-US-001', 'oppo_us_agent', '<EMAIL>', 1,
 '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5S/kS', 'salt_agent_us_001', NOW(),
 0, 'AGENT', 'PREMIUM',
 'OPPO美国代理', 'OPPO US Agent', 'https://cdn.dtf.com/avatar/oppo-us-agent.png', 'UNKNOWN',
 '+1-415-OPPO-001', 1, 'US', 'US', 'en', 'America/Los_Angeles',
 'ACTIVE', 'PREMIUM', DATE_ADD(NOW(), INTERVAL 2 YEAR),
 1073741824, 50000,
 1, 0, 'SYSTEM', NOW(), NOW()),

(7, 6, 'GLOBAL-AGENT-EU-001', 'LOCAL-AGENT-EU-001', 'xiaomi_eu_agent', '<EMAIL>', 1,
 '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5S/kS', 'salt_agent_eu_001', NOW(),
 0, 'AGENT', 'BASIC',
 '小米欧盟代理', 'Xiaomi EU Agent', 'https://cdn.dtf.com/avatar/xiaomi-eu-agent.png', 'UNKNOWN',
 '+49-800-MI-EU', 1, 'DE', 'EU', 'de', 'Europe/Berlin',
 'ACTIVE', 'BASIC', DATE_ADD(NOW(), INTERVAL 2 YEAR),
 *********, 30000,
 1, 0, 'SYSTEM', NOW(), NOW());

SET FOREIGN_KEY_CHECKS = 1;

-- =====================================================
-- 验证数据插入结果
-- =====================================================

-- 验证查询
SELECT 'Global Identities' as table_name, COUNT(*) as record_count
FROM dtf_global_identity
UNION ALL
SELECT 'Deployment Instances', COUNT(*)
FROM dtf_deployment_instance
UNION ALL
SELECT 'Tenants', COUNT(*)
FROM dtf_tenant
UNION ALL
SELECT 'User Accounts', COUNT(*)
FROM dtf_tenant_user_account;

-- 显示租户层级结构
SELECT t.id,
       t.tenant_code,
       t.tenant_name,
       t.tenant_type,
       t.hierarchy_level,
       t.tenant_hierarchy,
       t.version,
       t.deleted,
       p.tenant_name as parent_tenant_name
FROM dtf_tenant t
         LEFT JOIN dtf_tenant p ON t.parent_tenant_id = p.id
ORDER BY t.hierarchy_level, t.id;

-- 显示用户账户概览
SELECT u.id,
       u.username,
       u.email,
       u.user_type,
       u.user_level,
       u.version,
       u.deleted,
       t.tenant_name,
       u.account_status
FROM dtf_tenant_user_account u
         JOIN dtf_tenant t ON u.tenant_id = t.id
ORDER BY u.id;