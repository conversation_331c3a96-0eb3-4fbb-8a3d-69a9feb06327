spring:
  redis:
    database: 12
    host: *************
    password: 123456
    port: 6388
    timeout : 60s
    lettuce:
      pool:
        max-idle: 16
        max-active: 100
        max-wait: 16
        min-idle: 16
  cache:
    type: redis
  main:
    allow-circular-references: true
  mvc:
    static-path-pattern: /public/**
  web:
    resources:
      static-locations: classpath:config/public/
  messages:
    basename: i18n/messages
    encoding: UTF-8
    use-code-as-default-message: true


  rabbitmq:
    #tls消息队列协议端口5673,一定要配置正确的tls端口否则会连接失败抱timeout超时null
    port: 15678
    #为了开发简单先这样配置，部署测试和生产环境时要调整安全的账号
    username: admin
    #为了开发简单先这样配置，部署测试和生产环境时要调整安全的账号
    password: admin
    #为了开发简单先这样配置，部署测试和生产环境时要调整安全的账号
    virtual-host: /
    # 消息确认机制
    publisher-confirm-type: correlated
    publisher-returns: true
    listener:
      simple:
        retry:
          enabled: true # 开启消费者进行重试
          max-attempts: 1 # 最大重试次数
          initial-interval: 3000 # 重试时间间隔
        acknowledge-mode: manual #消费手动应答

  # Nacos服务发现配置
  cloud:
    nacos:
      discovery:
        heart-beat-interval: 10  # 增加心跳间隔
        heart-beat-timeout: 30   # 增加心跳超时
        ip-delete-timeout: 60    # 增加IP删除超时
        register-enabled: true
        weight: 1
        ephemeral: true
        # group: DEFAULT_GROUP
        cluster-name: DEFAULT
        failure-detection: true
        secure: false
        push-empty-protection: false

# dubbo
dubbo:
  application:
    serialize-check-status: WARN
    qos-enable: false
    logger: slf4j
    name: ${spring.application.name}
    enable-file-cache: false
  registry:
    id: pod-designer-nacos-registry
    address: nacos://${NACOS_CONFIG_ADDR:*************:8848}?namespace=${DUBBO-NAMESPACE:hoson-pod-dubbo}
    register-mode: instance
    client: curator
    simplified: true
    username: ${NACOS_USERNAME:nacos}
    password: ${NACOS_PASSWORD:Hosonsoft2021}
    parameters:
      subscribe.1.callback: true
      file: ${user.home}/.dubbo/dubbo-registry-${spring.application.name}.cache
      retry: 3
      retry.period: 5000  # 增加重试间隔
      session: 60000      # 增加会话超时
      connect: 15000      # 增加连接超时
  protocol:
    serialization: fastjson2
    name: dubbo
    # 使用-1让系统自动分配端口，避免冲突
    port: -1
    parameters:
      heartbeat: 60000           # 增加心跳间隔
      heartbeat.timeout: 120000  # 增加心跳超时
      connect.timeout: 10000     # 增加连接超时
  consumer:
    serialization: ${dubbo.protocol.serialization}
    timeout: 30000      # 统一增加超时时间
    retries: 2
    check: false
    validation: false
    filter: -authenticationPrepare
    loadbalance: leastactive
    cluster: failover
    connections: 10
    sticky: false
    cache: false
  provider:
    serialization: ${dubbo.protocol.serialization}
    timeout: 30000      # 与consumer保持一致
    validation: true
    filter: -exception,-generic
    threads: 200
    queues: 0
    threadpool: fixed
    mock: false

pod:
  #xxl-job定时任务调度的配置
  xxl-job:
    admin:
      admin-addresses: http://************:32574/xxl-job-admin
    executor:
      port: ${server.port}
      app-name: ${spring.application.name}
      access-token: default_token
      gatewayXxlExec: false
      #      router-address: http://localhost:2${server.port}
      log-path: /tmp/logs/${spring.application.name}/xxl-job
      log-retention-days: 30
  oss-minio:
    config:
      type: MYOSS
      myOssDomain: *************:9000 #域名ip地址
      myOssPort: 9000 #端口号
      myOssEndPoint: http://*************:9000/ #内置OSS服务EndPoint（域名ip地址+端口号）
      myOssAccessKeyId: admin #  账号
      myOssAccessKeySecret: 12345678 #  密码
      myOssBucketName: "hoson-pod"  # 桶的名字
      myOssPrefix: "download"  #内置OSS服务路径前缀
  oss-tencent:
    appId: **********
    bucketName: podesigner-test
    domain: https://podesigner-test-**********.cos.ap-guangzhou.myqcloud.com
    prefix: devupload
    region: ap-guangzhou
    secretId: AKID8GKI7ygDmJfrqgtyra7RXO2SenrlVAey
    secretKey: A7rQq4A2kIjz4xjJhuwnTyGJ8pOaczKX
    type: TENCENT



# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,dubbo,nacos
  endpoint:
    health:
      show-details: always
      # 健康检查配置
      probes:
        enabled: true
  health:
    # 显示详细健康信息
    show-details: always
    # 健康检查超时时间
    defaults:
      enabled: true
    # Nacos健康检查
    nacos:
      enabled: true

# Nacos服务发现优化配置（已合并到上面的spring配置中）

# MyBatis Plus公共配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
    default-enum-type-handler: com.baomidou.mybatisplus.core.handlers.MybatisEnumTypeHandler
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations: classpath*:/mapper/**/*.xml


# 日志配置
logging:
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"

# Hoson POD 业务配置
hoson:
  pod:
    tenant:
      enabled: true
      tenant-id-column: tenant_id

    sensitive:
      # 是否启用敏感数据脱敏功能
      enabled: false

      # 加密配置
      encryption:
        # 加密算法，目前支持AES
        algorithm: AES
        # 加密密钥，生产环境建议使用环境变量
        # 密钥长度建议32字符，确保安全性
        secret-key: ${SENSITIVE_ENCRYPTION_KEY:HosonPodSensitive2025DataEncryption}
        # 是否自动生成Hash索引用于查询
        auto-generate-hash: true

      # 脱敏策略配置
      strategies:
        # 姓名脱敏策略
        name:
          enabled: true
          # 自定义脱敏规则（可选）
          custom-pattern: ""

        # 手机号脱敏策略
        phone:
          enabled: true
          custom-pattern: ""

        # 邮箱脱敏策略
        email:
          enabled: true
          custom-pattern: ""

        # 地址脱敏策略
        address:
          enabled: true
          custom-pattern: ""

      # JSON序列化配置
      json:
        # 是否启用JSON序列化时脱敏
        enabled: true
        # 默认脱敏级别：NONE, LIGHT, STANDARD, HEAVY
        default-mask-level: STANDARD
