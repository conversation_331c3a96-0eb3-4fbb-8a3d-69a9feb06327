# 开发环境配置 - 简化版，避免与bootstrap.yml重复
spring:
  # Nacos配置由bootstrap.yml统一管理
  # 这里只配置开发环境特有的配置
  config:
    import:
      - optional:nacos:dtf-tool-common.yml?group=DEFAULT_GROUP&refreshEnabled=true
      - optional:nacos:${spring.application.name}.yml?group=DEFAULT_GROUP&refreshEnabled=true

  cloud:
    nacos:
      server-addr: ${NACOS_CONFIG_ADDR:192.168.1.251:8848}
      discovery:
        # 使用正确的命名空间ID
        namespace: ${NACOS_NAMESPACE:dtf-tool}
        username: ${NACOS_USERNAME:nacos}
        password: ${NACOS_PASSWORD:Hosonsoft2021}
      config:
        import-check:
          enabled: false
        # 使用正确的命名空间ID
        namespace: ${NACOS_NAMESPACE:dtf-tool}
        username: ${NACOS_USERNAME:nacos}
        password: ${NACOS_PASSWORD:Hosonsoft2021}
        group: DEFAULT_GROUP
        file-extension: yml
