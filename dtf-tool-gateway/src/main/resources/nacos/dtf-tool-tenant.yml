# 网关基础
spring:
  application:
    name: dtf-tool-gateway

    gateway:
      # 全局CORS（开发环境可用，生产请按域名收敛）
      globalcors:
        corsConfigurations:
          '[/**]':
            allowedOrigins: "*"
            allowedMethods: "*"
            allowedHeaders: "*"
            allowCredentials: true
      default-filters:
        - DedupeResponseHeader=Access-Control-Allow-Credentials Access-Control-Allow-Origin
      httpclient:
        connect-timeout: 10000
        response-timeout: 60s
        pool:
          max-connections: 200
          max-idle-time: 30s
      routes:
        # 账户/认证相关（指向 dtf-tool-tenant）
        - id: tenant-domain
          uri: lb://dtf-tool-tenant
          predicates:
            - Path=/api/v1/tenant/**
          filters:
            - StripPrefix=0

  # 负载均衡重试
  loadbalancer:
    retry:
      enabled: true
      max-retries-on-same-service-instance: 1
      max-retries-on-next-service-instance: 2
      retry-on-all-operations: false
    health-check:
      initial-delay: 5s
      interval: 30s

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,gateway
  endpoint:
    health:
      show-details: always

logging:
  level:
    root: INFO
    org.springframework.cloud.gateway: DEBUG
    reactor.netty: DEBUG
    com.alibaba.cloud.nacos: DEBUG
    com.alibaba.nacos: DEBUG